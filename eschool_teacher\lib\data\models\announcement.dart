import 'package:eschool_teacher/data/models/studyMaterial.dart';

class Announcement {
  Announcement({
    required this.id,
    required this.title,
    required this.description,
    required this.imageURI,
    required this.createdAt,
  });
  
  late final String id;
  late final String title;
  late final String description;
  late final String imageURI;
  late final DateTime createdAt;
  late final List<StudyMaterial> files;

  Announcement.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString() ?? "";
    title = json['title'] ?? "";
    description = json['description'] ?? "";
    imageURI = json['image_uri'] ?? json['image_url'] ?? "";
    createdAt = json['created_at'] == null
        ? DateTime.now()
        : DateTime.parse(json['created_at']);
    files = ((json['file'] ?? []) as List)
        .map((file) => StudyMaterial.fromJson(Map.from(file)))
        .toList();
  }

  // Method to add user (as per UML)
  void addUser() {
    // Implementation for adding user to announcement
  }
}
