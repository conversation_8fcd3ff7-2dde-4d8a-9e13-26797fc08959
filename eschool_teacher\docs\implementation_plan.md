# Teacher App Implementation Plan

## Feature Mapping (PRD to Current Teacher App)

| PRD Feature | Exists in Teacher App? | Exists in Student App? | Implementation Path |
|-------------|------------------------|------------------------|---------------------|
| **Authentication & Profile** |
| Login with OTP-based 2FA | ✅ Basic login exists | ❌ | Enhance existing login screen |
| Password reset via admin | ❌ | ✅ Password reset flow | Adapt from student app |
| 6-character unique ID | ❌ | ❌ | New feature |
| Profile editing | ✅ | ✅ | Enhance existing profile |
| **Zoom Integration** |
| Auto-generate meeting links | ❌ | ❌ | New feature |
| Cancel/reschedule meetings | ❌ | ❌ | New feature |
| Restrict access timing | ❌ | ❌ | New feature |
| **Lecture Management** |
| Create individual/group lessons | ✅ Partial | ❌ | Enhance existing |
| Schedule recurring sessions | ❌ | ❌ | New feature |
| Edit/delete lessons | ✅ | ❌ | Enhance existing |
| Reschedule limit tracking | ❌ | ❌ | New feature |
| Calendar view | ✅ Limited | ❌ | Enhance existing |
| **Earnings Tracking** |
| View earnings | ❌ | ❌ | New feature |
| Request withdrawal | ❌ | ❌ | New feature |
| Transaction history | ❌ | ❌ | New feature |
| **Assignments Module** |
| Create assignments | ✅ | ✅ | Enhance existing |
| Copy to multiple students | ❌ | ❌ | New feature |
| Review submissions | ✅ | ✅ | Enhance existing |
| Provide grades & comments | ✅ | ✅ | Enhance existing |
| **Chat Module** |
| Search by ID/name/role | ✅ Partial | ✅ | Port from student app |
| Technical support pinned chat | ❌ | ✅ | Port from student app |
| View profiles | ✅ | ✅ | Enhance existing |

## Core Modules Assessment

### 1. Authentication Module

**Current State:**
- Basic email/password authentication
- No 2FA implementation
- Limited password reset functionality

**Required Changes:**
- Add OTP generation and verification
- Implement admin-assisted password reset
- Create user ID display and search functionality
- Enhance security with session management
- Add role-based access control

### 2. Lecture Management Module

**Current State:**
- Basic lesson creation and management
- Limited scheduling options
- No recurring lesson support
- No rescheduling limits

**Required Changes:**
- Implement individual vs group lesson options
- Add recurring scheduling with exceptions
- Create reschedule tracking (max 2 times)
- Enhance calendar view for better visualization
- Integrate Zoom meeting generation

### 3. Assignments Module

**Current State:**
- Basic assignment creation
- Simple submission review
- Limited grading functionality

**Required Changes:**
- Add assignment duplication to multiple students
- Enhance grading interface with better feedback options
- Improve file attachment handling
- Add assignment editing before due date

### 4. Chat Module

**Current State:**
- Basic messaging functionality
- Limited user search capabilities
- No technical support integration

**Required Changes:**
- Enhance user search by ID, name, or role
- Add technical support pinned chat
- Improve profile viewing
- Add message status indicators

### 5. Earnings Module (New)

**Required Implementation:**
- Create earnings tracking dashboard
- Implement withdrawal request system
- Build transaction history view
- Add IBAN management
- Create status tracking for withdrawal requests

## Development Approach

1. **Leverage Existing Code:**
   - Keep the core architecture and navigation
   - Reuse UI components where appropriate
   - Maintain state management patterns (BLoC/Cubit)

2. **Refactor and Enhance:**
   - Update models to support new features
   - Enhance repositories to handle new API endpoints
   - Update UI to match new design requirements

3. **New Feature Development:**
   - Create new cubits for earnings and Zoom integration
   - Develop models for these new features
   - Implement UI components following the design system

4. **Integration Strategy:**
   - Ensure chat works between teacher and student apps
   - Verify assignment submission and review flow
   - Test lesson booking from student and creation from teacher

## Immediate Next Steps

1. Create/update models for:
   - Earnings and withdrawals
   - Zoom meetings
   - Enhanced lessons with recurring options

2. Update the authentication flow to support 2FA

3. Begin UI enhancements for the home screen and navigation

4. Implement the earnings dashboard UI 