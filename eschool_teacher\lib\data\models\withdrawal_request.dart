class WithdrawalRequest {
  final String id;
  final double amount;
  final DateTime requestDate;
  final String status; // "pending", "approved", "rejected"
  final String? rejectReason;
  final String? iban;
  
  WithdrawalRequest({
    required this.id,
    required this.amount,
    required this.requestDate,
    required this.status,
    this.rejectReason,
    this.iban,
  });
  
  factory WithdrawalRequest.fromJson(Map<String, dynamic> json) {
    return WithdrawalRequest(
      id: json['id'] as String? ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      requestDate: json['request_date'] != null 
          ? DateTime.parse(json['request_date'] as String)
          : DateTime.now(),
      status: json['status'] as String? ?? 'pending',
      rejectReason: json['reject_reason'] as String?,
      iban: json['iban'] as String?,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'request_date': requestDate.toIso8601String(),
      'status': status,
      'reject_reason': rejectReason,
      'iban': iban,
    };
  }
} 