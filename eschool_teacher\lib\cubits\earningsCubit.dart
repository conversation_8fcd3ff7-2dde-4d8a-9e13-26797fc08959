import 'package:bloc/bloc.dart';
import 'package:eschool_teacher/data/models/earnings.dart';
import 'package:eschool_teacher/data/models/transaction.dart';
import 'package:eschool_teacher/data/models/withdrawal_request.dart';
import 'package:eschool_teacher/data/repositories/earningsRepository.dart';
import 'package:equatable/equatable.dart';

//state
abstract class EarningsState extends Equatable {
  const EarningsState();

  @override
  List<Object?> get props => [];
}

class EarningsInitial extends EarningsState {}

class EarningsFetchInProgress extends EarningsState {}

class EarningsFetchSuccess extends EarningsState {
  final Earnings earnings;

  const EarningsFetchSuccess({required this.earnings});

  @override
  List<Object?> get props => [earnings];
}

class EarningsFetchFailure extends EarningsState {
  final String errorMessage;

  const EarningsFetchFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

//transactions states
class TransactionsFetchInProgress extends EarningsState {}

class TransactionsFetchSuccess extends EarningsState {
  final List<Transaction> transactions;
  final int totalPages;
  final int currentPage;

  const TransactionsFetchSuccess({
    required this.transactions,
    required this.totalPages,
    required this.currentPage,
  });

  @override
  List<Object?> get props => [transactions, totalPages, currentPage];
}

class TransactionsFetchFailure extends EarningsState {
  final String errorMessage;

  const TransactionsFetchFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

//withdrawal request states
class WithdrawalRequestInProgress extends EarningsState {}

class WithdrawalRequestSuccess extends EarningsState {
  final WithdrawalRequest withdrawalRequest;

  const WithdrawalRequestSuccess({required this.withdrawalRequest});

  @override
  List<Object?> get props => [withdrawalRequest];
}

class WithdrawalRequestFailure extends EarningsState {
  final String errorMessage;

  const WithdrawalRequestFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

//withdrawal requests fetch states
class WithdrawalRequestsFetchInProgress extends EarningsState {}

class WithdrawalRequestsFetchSuccess extends EarningsState {
  final List<WithdrawalRequest> withdrawalRequests;
  final int totalPages;
  final int currentPage;

  const WithdrawalRequestsFetchSuccess({
    required this.withdrawalRequests,
    required this.totalPages,
    required this.currentPage,
  });

  @override
  List<Object?> get props => [withdrawalRequests, totalPages, currentPage];
}

class WithdrawalRequestsFetchFailure extends EarningsState {
  final String errorMessage;

  const WithdrawalRequestsFetchFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

//IBAN states
class IbanUpdateInProgress extends EarningsState {}

class IbanUpdateSuccess extends EarningsState {}

class IbanUpdateFailure extends EarningsState {
  final String errorMessage;

  const IbanUpdateFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

class IbanFetchInProgress extends EarningsState {}

class IbanFetchSuccess extends EarningsState {
  final String iban;

  const IbanFetchSuccess({required this.iban});

  @override
  List<Object?> get props => [iban];
}

class IbanFetchFailure extends EarningsState {
  final String errorMessage;

  const IbanFetchFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

//payment settings states
class PaymentSettingsFetchInProgress extends EarningsState {}

class PaymentSettingsFetchSuccess extends EarningsState {
  final String bankName;
  final String accountNumber;

  const PaymentSettingsFetchSuccess({
    required this.bankName,
    required this.accountNumber,
  });

  @override
  List<Object?> get props => [bankName, accountNumber];
}

class PaymentSettingsFetchFailure extends EarningsState {
  final String errorMessage;

  const PaymentSettingsFetchFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

//payment settings update states
class PaymentSettingsUpdateInProgress extends EarningsState {}

class PaymentSettingsUpdateSuccess extends EarningsState {}

class PaymentSettingsUpdateFailure extends EarningsState {
  final String errorMessage;

  const PaymentSettingsUpdateFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

//cubit
class EarningsCubit extends Cubit<EarningsState> {
  final EarningsRepository _earningsRepository;

  EarningsCubit(this._earningsRepository) : super(EarningsInitial());

  // Fetch teacher's earnings summary
  Future<void> fetchEarnings() async {
    try {
      emit(EarningsFetchInProgress());
      final Map<String, dynamic> result = await _earningsRepository.fetchEarnings();
      
      if (result['error']) {
        emit(EarningsFetchFailure(errorMessage: result['message']));
      } else {
        emit(EarningsFetchSuccess(earnings: result['earnings']));
      }
    } catch (e) {
      emit(EarningsFetchFailure(errorMessage: e.toString()));
    }
  }

  // Fetch teacher's transaction history
  Future<void> fetchTransactions({
    required int page,
    String? type,
    String? startDate,
    String? endDate,
  }) async {
    try {
      emit(TransactionsFetchInProgress());
      final Map<String, dynamic> result = await _earningsRepository.fetchTransactions(
        page: page,
        type: type,
        startDate: startDate,
        endDate: endDate,
      );
      
      if (result['error']) {
        emit(TransactionsFetchFailure(errorMessage: result['message']));
      } else {
        emit(TransactionsFetchSuccess(
          transactions: result['transactions'],
          totalPages: result['totalPages'],
          currentPage: result['currentPage'],
        ));
      }
    } catch (e) {
      emit(TransactionsFetchFailure(errorMessage: e.toString()));
    }
  }

  // Request withdrawal
  Future<void> requestWithdrawal({
    required double amount,
    required String iban,
  }) async {
    try {
      emit(WithdrawalRequestInProgress());
      final Map<String, dynamic> result = await _earningsRepository.requestWithdrawal(
        amount: amount,
        iban: iban,
      );
      
      if (result['error']) {
        emit(WithdrawalRequestFailure(errorMessage: result['message']));
      } else {
        emit(WithdrawalRequestSuccess(withdrawalRequest: result['withdrawalRequest']));
      }
    } catch (e) {
      emit(WithdrawalRequestFailure(errorMessage: e.toString()));
    }
  }

  // Fetch withdrawal requests history
  Future<void> fetchWithdrawalRequests({
    required int page,
    String? status,
  }) async {
    try {
      emit(WithdrawalRequestsFetchInProgress());
      final Map<String, dynamic> result = await _earningsRepository.fetchWithdrawalRequests(
        page: page,
        status: status,
      );
      
      if (result['error']) {
        emit(WithdrawalRequestsFetchFailure(errorMessage: result['message']));
      } else {
        emit(WithdrawalRequestsFetchSuccess(
          withdrawalRequests: result['withdrawalRequests'],
          totalPages: result['totalPages'],
          currentPage: result['currentPage'],
        ));
      }
    } catch (e) {
      emit(WithdrawalRequestsFetchFailure(errorMessage: e.toString()));
    }
  }

  // Update IBAN
  Future<void> updateIban({
    required String iban,
  }) async {
    try {
      emit(IbanUpdateInProgress());
      final Map<String, dynamic> result = await _earningsRepository.updateIban(
        iban: iban,
      );
      
      if (result['error']) {
        emit(IbanUpdateFailure(errorMessage: result['message']));
      } else {
        emit(IbanUpdateSuccess());
      }
    } catch (e) {
      emit(IbanUpdateFailure(errorMessage: e.toString()));
    }
  }

  // Get IBAN
  Future<void> getIban() async {
    try {
      emit(IbanFetchInProgress());
      final Map<String, dynamic> result = await _earningsRepository.getIban();
      
      if (result['error']) {
        emit(IbanFetchFailure(errorMessage: result['message']));
      } else {
        emit(IbanFetchSuccess(iban: result['iban']));
      }
    } catch (e) {
      emit(IbanFetchFailure(errorMessage: e.toString()));
    }
  }

  // Get payment settings
  Future<void> getPaymentSettings() async {
    try {
      emit(PaymentSettingsFetchInProgress());
      final Map<String, dynamic> result = await _earningsRepository.getPaymentSettings();
      
      if (result['error']) {
        emit(PaymentSettingsFetchFailure(errorMessage: result['message']));
      } else {
        emit(PaymentSettingsFetchSuccess(
          bankName: result['bankName'],
          accountNumber: result['accountNumber'],
        ));
      }
    } catch (e) {
      emit(PaymentSettingsFetchFailure(errorMessage: e.toString()));
    }
  }

  // Update payment settings
  Future<void> updatePaymentSettings({
    required String bankName,
    required String accountNumber,
  }) async {
    try {
      emit(PaymentSettingsUpdateInProgress());
      final Map<String, dynamic> result = await _earningsRepository.updatePaymentSettings(
        bankName: bankName,
        accountNumber: accountNumber,
      );
      
      if (result['error']) {
        emit(PaymentSettingsUpdateFailure(errorMessage: result['message']));
      } else {
        emit(PaymentSettingsUpdateSuccess());
      }
    } catch (e) {
      emit(PaymentSettingsUpdateFailure(errorMessage: e.toString()));
    }
  }
} 