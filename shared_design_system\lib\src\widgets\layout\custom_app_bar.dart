import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/colors.dart';
import '../../tokens/spacing.dart';
import '../../tokens/typography.dart';
import '../../tokens/shadows.dart';

/// Shared app bar widget following the design system
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.leading,
    this.actions,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = true,
    this.automaticallyImplyLeading = true,
    this.systemOverlayStyle,
    this.toolbarHeight,
    this.bottom,
    this.flexibleSpace,
    this.shadowColor,
    this.shape,
    this.titleSpacing,
    this.leadingWidth,
    this.actionsIconTheme,
    this.iconTheme,
    this.titleTextStyle,
    this.onLeadingPressed,
    this.showBackButton = false,
    this.backButtonColor,
  });

  /// App bar title text
  final String? title;

  /// Custom title widget (overrides title)
  final Widget? titleWidget;

  /// Leading widget
  final Widget? leading;

  /// Action widgets
  final List<Widget>? actions;

  /// Background color
  final Color? backgroundColor;

  /// Foreground color (text, icons)
  final Color? foregroundColor;

  /// Elevation
  final double? elevation;

  /// Whether to center the title
  final bool centerTitle;

  /// Whether to automatically show leading button
  final bool automaticallyImplyLeading;

  /// System UI overlay style
  final SystemUiOverlayStyle? systemOverlayStyle;

  /// Toolbar height
  final double? toolbarHeight;

  /// Bottom widget (e.g., TabBar)
  final PreferredSizeWidget? bottom;

  /// Flexible space widget
  final Widget? flexibleSpace;

  /// Shadow color
  final Color? shadowColor;

  /// App bar shape
  final ShapeBorder? shape;

  /// Title spacing
  final double? titleSpacing;

  /// Leading widget width
  final double? leadingWidth;

  /// Actions icon theme
  final IconThemeData? actionsIconTheme;

  /// Icon theme
  final IconThemeData? iconTheme;

  /// Title text style
  final TextStyle? titleTextStyle;

  /// Callback for leading button press
  final VoidCallback? onLeadingPressed;

  /// Whether to show back button
  final bool showBackButton;

  /// Back button color
  final Color? backButtonColor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Determine colors
    final effectiveBackgroundColor = backgroundColor ?? AppColors.primary;
    final effectiveForegroundColor = foregroundColor ?? AppColors.onPrimary;
    final effectiveShadowColor = shadowColor ?? AppColors.shadow;
    
    // Determine system overlay style
    final effectiveSystemOverlayStyle = systemOverlayStyle ?? 
        (effectiveBackgroundColor.computeLuminance() > 0.5
            ? SystemUiOverlayStyle.dark
            : SystemUiOverlayStyle.light);

    // Build leading widget
    Widget? effectiveLeading = leading;
    if (leading == null && showBackButton) {
      effectiveLeading = IconButton(
        onPressed: onLeadingPressed ?? () => Navigator.of(context).pop(),
        icon: Icon(
          Icons.arrow_back,
          color: backButtonColor ?? effectiveForegroundColor,
        ),
      );
    }

    // Build title widget
    Widget? effectiveTitleWidget = titleWidget;
    if (titleWidget == null && title != null) {
      effectiveTitleWidget = Text(
        title!,
        style: (titleTextStyle ?? AppTypography.appBarTitle).copyWith(
          color: effectiveForegroundColor,
        ),
      );
    }

    return AppBar(
      title: effectiveTitleWidget,
      leading: effectiveLeading,
      actions: actions,
      backgroundColor: effectiveBackgroundColor,
      foregroundColor: effectiveForegroundColor,
      elevation: elevation ?? AppElevation.appBar,
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      systemOverlayStyle: effectiveSystemOverlayStyle,
      toolbarHeight: toolbarHeight ?? AppSpacing.appBarHeight,
      bottom: bottom,
      flexibleSpace: flexibleSpace,
      shadowColor: effectiveShadowColor,
      shape: shape,
      titleSpacing: titleSpacing,
      leadingWidth: leadingWidth,
      actionsIconTheme: actionsIconTheme?.copyWith(
        color: actionsIconTheme?.color ?? effectiveForegroundColor,
      ),
      iconTheme: iconTheme?.copyWith(
        color: iconTheme?.color ?? effectiveForegroundColor,
      ),
      titleTextStyle: titleTextStyle?.copyWith(
        color: titleTextStyle?.color ?? effectiveForegroundColor,
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    (toolbarHeight ?? AppSpacing.appBarHeight) + 
    (bottom?.preferredSize.height ?? 0),
  );
}

/// Pre-built app bar variants for common use cases
class AppBarVariants {
  AppBarVariants._();

  /// Standard app bar with title
  static PreferredSizeWidget standard({
    required String title,
    List<Widget>? actions,
    VoidCallback? onBackPressed,
    bool showBackButton = false,
  }) {
    return CustomAppBar(
      title: title,
      actions: actions,
      onLeadingPressed: onBackPressed,
      showBackButton: showBackButton,
    );
  }

  /// App bar with custom title widget
  static PreferredSizeWidget withCustomTitle({
    required Widget titleWidget,
    List<Widget>? actions,
    VoidCallback? onBackPressed,
    bool showBackButton = false,
  }) {
    return CustomAppBar(
      titleWidget: titleWidget,
      actions: actions,
      onLeadingPressed: onBackPressed,
      showBackButton: showBackButton,
    );
  }

  /// App bar with search functionality
  static PreferredSizeWidget search({
    required TextEditingController searchController,
    required String hintText,
    ValueChanged<String>? onSearchChanged,
    VoidCallback? onSearchClear,
    VoidCallback? onBackPressed,
    List<Widget>? actions,
  }) {
    return CustomAppBar(
      titleWidget: Container(
        height: 40,
        margin: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
        decoration: BoxDecoration(
          color: AppColors.surface.withOpacity(0.2),
          borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
        ),
        child: TextField(
          controller: searchController,
          onChanged: onSearchChanged,
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.onPrimary,
          ),
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: AppTypography.bodyMedium.copyWith(
              color: AppColors.onPrimary.withOpacity(0.7),
            ),
            prefixIcon: Icon(
              Icons.search,
              color: AppColors.onPrimary.withOpacity(0.7),
              size: AppSpacing.iconMd,
            ),
            suffixIcon: searchController.text.isNotEmpty
                ? IconButton(
                    onPressed: () {
                      searchController.clear();
                      onSearchClear?.call();
                    },
                    icon: Icon(
                      Icons.clear,
                      color: AppColors.onPrimary.withOpacity(0.7),
                      size: AppSpacing.iconMd,
                    ),
                  )
                : null,
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.sm,
            ),
          ),
        ),
      ),
      onLeadingPressed: onBackPressed,
      showBackButton: true,
      actions: actions,
    );
  }

  /// App bar with tabs
  static PreferredSizeWidget withTabs({
    required String title,
    required TabBar tabBar,
    List<Widget>? actions,
    VoidCallback? onBackPressed,
    bool showBackButton = false,
  }) {
    return CustomAppBar(
      title: title,
      actions: actions,
      onLeadingPressed: onBackPressed,
      showBackButton: showBackButton,
      bottom: tabBar,
    );
  }

  /// App bar for settings/profile screens
  static PreferredSizeWidget profile({
    required String title,
    VoidCallback? onBackPressed,
    List<Widget>? actions,
  }) {
    return CustomAppBar(
      title: title,
      onLeadingPressed: onBackPressed,
      showBackButton: true,
      actions: actions,
      backgroundColor: AppColors.surface,
      foregroundColor: AppColors.onSurface,
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle.dark,
    );
  }

  /// Transparent app bar for full-screen content
  static PreferredSizeWidget transparent({
    Widget? titleWidget,
    List<Widget>? actions,
    VoidCallback? onBackPressed,
    bool showBackButton = true,
  }) {
    return CustomAppBar(
      titleWidget: titleWidget,
      actions: actions,
      onLeadingPressed: onBackPressed,
      showBackButton: showBackButton,
      backgroundColor: Colors.transparent,
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle.light,
    );
  }

  /// App bar with gradient background
  static PreferredSizeWidget gradient({
    required String title,
    required Gradient gradient,
    List<Widget>? actions,
    VoidCallback? onBackPressed,
    bool showBackButton = false,
  }) {
    return CustomAppBar(
      title: title,
      actions: actions,
      onLeadingPressed: onBackPressed,
      showBackButton: showBackButton,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: BoxDecoration(gradient: gradient),
      ),
    );
  }
} 