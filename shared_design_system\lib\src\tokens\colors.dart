import 'package:flutter/material.dart';

/// Design system color tokens for eSchool apps
class AppColors {
  AppColors._();

  // Primary Colors (from teacher app design)
  static const Color primary = Color(0xFF5F97B7);
  static const Color primaryDark = Color(0xFF4A7A96);
  static const Color primaryLight = Color(0xFF7FABC5);
  
  // Highlight/Accent Colors
  static const Color highlight = Color(0xFF25536D);
  static const Color highlightDark = Color(0xFF1E4257);
  static const Color highlightLight = Color(0xFF3A6B83);
  
  // Background Colors
  static const Color background = Color(0xFFE9E9E9);
  static const Color backgroundLight = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF8F9FA);
  
  // Text Colors
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF1C1B1F);
  static const Color onSurfaceVariant = Color(0xFF49454F);
  static const Color onBackground = Color(0xFF1C1B1F);
  
  // Semantic Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFFE8F5E8);
  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFF3E0);
  static const Color error = Color(0xFFE53935);
  static const Color errorLight = Color(0xFFFFEBEE);
  static const Color info = Color(0xFF2196F3);
  static const Color infoLight = Color(0xFFE3F2FD);
  
  // Neutral Colors
  static const Color neutral50 = Color(0xFFFAFAFA);
  static const Color neutral100 = Color(0xFFF5F5F5);
  static const Color neutral200 = Color(0xFFEEEEEE);
  static const Color neutral300 = Color(0xFFE0E0E0);
  static const Color neutral400 = Color(0xFFBDBDBD);
  static const Color neutral500 = Color(0xFF9E9E9E);
  static const Color neutral600 = Color(0xFF757575);
  static const Color neutral700 = Color(0xFF616161);
  static const Color neutral800 = Color(0xFF424242);
  static const Color neutral900 = Color(0xFF212121);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderLight = Color(0xFFF0F0F0);
  static const Color borderDark = Color(0xFFBDBDBD);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  static const Color shadowDark = Color(0x26000000);
  
  // Status Colors for different states
  static const Color pending = Color(0xFFFF9800);
  static const Color inProgress = Color(0xFF2196F3);
  static const Color completed = Color(0xFF4CAF50);
  static const Color cancelled = Color(0xFF757575);
  static const Color overdue = Color(0xFFE53935);
  
  // App-specific colors that can be customized per app
  static const Color studentAccent = Color(0xFF6C63FF);
  static const Color teacherAccent = Color(0xFF25536D);
}

/// Color scheme for the eSchool design system
class AppColorScheme {
  AppColorScheme._();
  
  static const ColorScheme light = ColorScheme.light(
    primary: AppColors.primary,
    onPrimary: AppColors.onPrimary,
    secondary: AppColors.highlight,
    onSecondary: AppColors.onPrimary,
    surface: AppColors.surface,
    onSurface: AppColors.onSurface,
    background: AppColors.background,
    onBackground: AppColors.onBackground,
    error: AppColors.error,
    onError: AppColors.onPrimary,
    surfaceVariant: AppColors.surfaceVariant,
    onSurfaceVariant: AppColors.onSurfaceVariant,
  );
} 