<!DOCTYPE html>
<html>
<head>
    <title>Google ID Token Generator</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>
    <h1>Google ID Token for API Testing</h1>
    <div id="g_id_onload"
         data-client_id="YOUR_GOOGLE_CLIENT_ID_HERE"
         data-callback="handleCredentialResponse"
         data-auto_prompt="false">
    </div>
    <div class="g_id_signin" data-type="standard"></div>
    
    <div id="token-display" style="margin-top: 20px; padding: 10px; background-color: #f0f0f0; word-break: break-all;">
        <p><strong>Your Google ID Token will appear here:</strong></p>
        <div id="token-value"></div>
    </div>

    <script>
        function handleCredentialResponse(response) {
            console.log("Encoded JWT ID token: " + response.credential);
            document.getElementById('token-value').innerHTML = response.credential;
            
            // Also decode and display the payload for verification
            const base64Url = response.credential.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
            
            console.log("Decoded payload:", JSON.parse(jsonPayload));
        }
    </script>
</body>
</html> 