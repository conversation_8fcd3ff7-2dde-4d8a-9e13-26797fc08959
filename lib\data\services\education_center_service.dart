import 'package:flutter/foundation.dart';
import 'package:eschool/data/services/api_service_wrapper.dart';
import 'package:eschool/data/repositories/subjectRepository.dart';
import 'package:eschool/data/repositories/packageRepository.dart';
import 'package:eschool/data/services/dummy_data_service.dart';

/// Education Center Service
/// 
/// This service demonstrates the API-first with dummy data fallback pattern.
/// It provides a unified interface for all education center operations.
/// 
/// Usage:
/// ```dart
/// final service = EducationCenterService();
/// 
/// // Fetch subjects (will try API first, fallback to dummy data if enabled)
/// final subjects = await service.getSubjects();
/// 
/// // Fetch packages with filters
/// final packages = await service.getPackages(subjectId: 1, level: 'تأسيسي');
/// 
/// // Check service status
/// final status = service.getServiceStatus();
/// ```
class EducationCenterService {
  final ApiServiceWrapper _apiServiceWrapper;
  final SubjectRepository _subjectRepository;
  final PackageRepository _packageRepository;
  
  // Singleton pattern
  static final EducationCenterService _instance = EducationCenterService._internal();
  factory EducationCenterService() => _instance;
  
  EducationCenterService._internal()
      : _apiServiceWrapper = ApiServiceWrapper(),
        _subjectRepository = SubjectRepository(),
        _packageRepository = PackageRepository();

  // ======== SUBJECTS ========
  
  /// Get all subjects with API-first fallback pattern
  Future<Map<String, dynamic>> getSubjects({String? token}) async {
    debugPrint('EducationCenterService: Fetching subjects');
    return await _subjectRepository.fetchStudentSubjects(token: token);
  }
  
  /// Get subject by ID
  Future<Map<String, dynamic>> getSubjectById({
    required int subjectId,
    String? token,
  }) async {
    debugPrint('EducationCenterService: Fetching subject $subjectId');
    return await _apiServiceWrapper.fetchSubjectById(
      subjectId: subjectId,
      token: token,
    );
  }

  // ======== PACKAGES ========
  
  /// Get packages with optional filters
  Future<List<dynamic>> getPackages({
    int? subjectId,
    String? level,
    String? token,
  }) async {
    debugPrint('EducationCenterService: Fetching packages (subject: $subjectId, level: $level)');
    
    try {
      final packages = await _packageRepository.fetchPackages(
        subject: subjectId,
        level: level,
        token: token,
      );
      
      return packages.map((package) => package.toJson()).toList();
    } catch (e) {
      debugPrint('EducationCenterService: Error fetching packages: $e');
      rethrow;
    }
  }

  // ======== LESSONS ========
  
  /// Get lessons for a subject
  Future<Map<String, dynamic>> getLessons({
    int? subjectId,
    String? token,
  }) async {
    debugPrint('EducationCenterService: Fetching lessons for subject $subjectId');
    return await _subjectRepository.fetchSubjectLessons(
      subjectId: subjectId ?? 0,
      token: token,
    );
  }
  
  /// Get lesson details by ID
  Future<Map<String, dynamic>> getLessonById({
    required int lessonId,
    String? token,
  }) async {
    debugPrint('EducationCenterService: Fetching lesson $lessonId');
    return await _subjectRepository.fetchStudyMaterials(
      lessonId: lessonId,
      token: token,
    );
  }

  // ======== DIGITAL ASSETS ========
  
  /// Get digital assets (marketplace items)
  Future<Map<String, dynamic>> getDigitalAssets({String? token}) async {
    debugPrint('EducationCenterService: Fetching digital assets');
    return await _apiServiceWrapper.fetchDigitalAssets(token: token);
  }

  // ======== AUTHENTICATION ========
  
  /// Login (for demo purposes - typically handled by AuthRepository)
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    debugPrint('EducationCenterService: Attempting login');
    return await _apiServiceWrapper.login(
      email: email,
      password: password,
    );
  }

  // ======== UTILITY METHODS ========
  
  /// Check API connection status
  Future<bool> checkApiConnection() async {
    return await _apiServiceWrapper.checkApiConnection();
  }
  
  /// Get current service status including dummy data configuration
  Map<String, dynamic> getServiceStatus() {
    return {
      'service': 'EducationCenterService',
      'apiWrapper': _apiServiceWrapper.getServiceStatus(),
      'dummyDataConfig': DummyDataService.getStatus(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  /// Enable dummy data mode (for testing/demo)
  void enableDummyDataMode() {
    DummyDataService.enableDummyData();
    debugPrint('EducationCenterService: Dummy data mode enabled');
  }
  
  /// Disable dummy data mode
  void disableDummyDataMode() {
    DummyDataService.disableDummyData();
    debugPrint('EducationCenterService: Dummy data mode disabled');
  }
  
  /// Force API connection failure (for testing fallback)
  void simulateApiFailure() {
    DummyDataService.setApiConnectionStatus(true);
    debugPrint('EducationCenterService: API failure simulated');
  }
  
  /// Reset API connection status
  void resetApiStatus() {
    DummyDataService.setApiConnectionStatus(false);
    debugPrint('EducationCenterService: API status reset');
  }

  // ======== DEMO METHODS ========
  
  /// Demonstrate the API-first with fallback pattern
  Future<void> demonstrateApiPattern() async {
    debugPrint('\n🚀 EducationCenterService: Demonstrating API-first with fallback pattern\n');
    
    // Show current status
    final status = getServiceStatus();
    debugPrint('📊 Current Status: ${status['dummyDataConfig']}');
    
    try {
      // 1. Try normal API call
      debugPrint('\n1️⃣ Attempting normal API call...');
      final subjects = await getSubjects();
      debugPrint('✅ Subjects fetched: ${subjects['data']?.length ?? 0} items');
      
      // 2. Simulate API failure and show fallback
      debugPrint('\n2️⃣ Simulating API failure...');
      simulateApiFailure();
      
      final packagesWithFallback = await getPackages();
      debugPrint('✅ Packages fetched with fallback: ${packagesWithFallback.length} items');
      
      // 3. Reset and try again
      debugPrint('\n3️⃣ Resetting API status...');
      resetApiStatus();
      
      final lessonsAfterReset = await getLessons(subjectId: 1);
      debugPrint('✅ Lessons fetched after reset: ${lessonsAfterReset['data']?.length ?? 0} items');
      
    } catch (e) {
      debugPrint('❌ Demo error: $e');
    }
    
    debugPrint('\n🎯 Demo completed!\n');
  }
}
