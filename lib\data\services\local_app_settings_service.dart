import 'package:flutter/foundation.dart';

/// Local app settings service that provides configuration data
/// without needing to make API calls to external services
class LocalAppSettingsService {
  static LocalAppSettingsService? _instance;
  static LocalAppSettingsService get instance => _instance ??= LocalAppSettingsService._();
  
  LocalAppSettingsService._();

  /// Get app settings data in the same format as the API response
  Map<String, dynamic> getAppSettings() {
    debugPrint('LocalAppSettingsService: Using local app settings data');
    
    return {
      "error": false,
      "message": "Data Fetched Successfully (Local)",
      "data": _getAppSettingsData(),
      "code": 200
    };
  }

  /// Get the app settings data structure
  Map<String, dynamic> _getAppSettingsData() {
    return {
      "app_link": "https://play.google.com/store/apps/details?id=com.mahranstudio.ecenter",
      "ios_app_link": "https://apps.apple.com/app/ecenter",
      "app_version": "1.0.0",
      "ios_app_version": "1.0.0",
      "force_app_update": "0",
      "app_maintenance": "0",
      "session_year": {
        "id": 1,
        "name": "2024-25",
        "free_app_use_date": "2025-04-01",
        "default": 1,
        "start_date": "2024-04-01",
        "end_date": "2025-04-01",
        "include_fee_installments": 1,
        "fee_due_date": "2025-01-01",
        "fee_due_charges": 5,
        "created_at": "2024-04-23T05:59:32.000000Z",
        "updated_at": "2024-04-23T06:14:03.000000Z",
        "deleted_at": null
      },
      "school_name": "eCenter - Education Center Management System",
      "school_tagline": "eCenter - Your Gateway to Quality Education",
      "teacher_app_link": "https://play.google.com/store/apps/details?id=com.mahranstudio.ecenter.teacher",
      "teacher_ios_app_link": "https://apps.apple.com/app/ecenter-teacher",
      "teacher_app_version": "1.0.0",
      "teacher_ios_app_version": "1.0.0",
      "teacher_force_app_update": "0",
      "teacher_app_maintenance": "0",
      "online_payment": "1",
      "is_demo": true,
      "compulsory_fee_payment_mode": "0",
      "is_student_can_pay_fees": "1",
      "chat_settings": {
        "max_files_or_images_in_one_message": "10",
        "max_file_size_in_bytes": 10000000,
        "max_characters_in_text_message": "500",
        "automatically_messages_removed_days": "7"
      },
      "holiday_days": "Friday,Saturday",
      "payment_options": {
        "currency_code": "qar",
        "currency_symbol": "ر.ق",
        "fees_due_date": "2025-06-30",
        "fees_due_charges": "100",
        "razorpay": {
          "razorpay_status": "0",
          "razorpay_api_key": "",
          "razorpay_webhook_secret": "",
          "razorpay_currency_code": "QAR"
        },
        "stripe": {
          "stripe_status": "1",
          "stripe_publishable_key": "pk_test_your_stripe_key_here",
          "stripe_currency_code": "QAR"
        },
        "paystack": {
          "paystack_status": "0",
          "paystack_public_key": "",
          "paystack_currency_code": "QAR"
        },
        "flutterwave": {
          "flutterwave_status": "0",
          "flutterwave_public_key": "",
          "flutterwave_currency_code": "QAR"
        }
      },
      "online_exam_terms_condition": """
<ul>
<li>Maintain academic integrity throughout the examination</li>
<li>Ensure stable internet connection</li>
<li>Keep your camera on during the exam</li>
<li>No external assistance or materials allowed</li>
<li>Complete the exam within the allocated time</li>
<li>Report any technical issues immediately</li>
</ul>
""",
      "current_semester": {
        "id": 1,
        "name": "Semester 1",
        "start_month": 9,
        "end_month": 1,
        "created_at": "2024-09-01T00:00:00.000000Z",
        "updated_at": "2024-09-01T00:00:00.000000Z",
        "deleted_at": null,
        "current": true,
        "start_month_name": "September",
        "end_month_name": "January"
      }
    };
  }

  /// Get specific setting value by key path (e.g., "school_name", "payment_options.currency_code")
  dynamic getSetting(String keyPath) {
    final data = _getAppSettingsData();
    final keys = keyPath.split('.');
    
    dynamic current = data;
    for (final key in keys) {
      if (current is Map<String, dynamic> && current.containsKey(key)) {
        current = current[key];
      } else {
        return null;
      }
    }
    
    return current;
  }

  /// Check if app is in demo mode
  bool get isDemoMode => getSetting('is_demo') == true;

  /// Get school name
  String get schoolName => getSetting('school_name') ?? 'eCenter';

  /// Get school tagline
  String get schoolTagline => getSetting('school_tagline') ?? 'Education Center Management';

  /// Get currency code
  String get currencyCode => getSetting('payment_options.currency_code') ?? 'qar';

  /// Get currency symbol
  String get currencySymbol => getSetting('payment_options.currency_symbol') ?? 'ر.ق';

  /// Check if online payment is enabled
  bool get isOnlinePaymentEnabled => getSetting('online_payment') == '1';

  /// Get app version
  String get appVersion => getSetting('app_version') ?? '1.0.0';

  /// Get iOS app version
  String get iosAppVersion => getSetting('ios_app_version') ?? '1.0.0';

  /// Check if force update is required
  bool get isForceUpdateRequired => getSetting('force_app_update') == '1';

  /// Check if app is under maintenance
  bool get isAppUnderMaintenance => getSetting('app_maintenance') == '1';

  /// Get holiday days as list
  List<String> get holidayDays {
    final days = getSetting('holiday_days') ?? '';
    return days.toString().split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
  }
}
