import 'package:flutter/foundation.dart';
import 'package:eschool/data/services/api_service.dart';
import 'package:eschool/data/services/dummy_data_service.dart';
import 'package:eschool/utils/api.dart';

/// Unified API Service Wrapper that implements API-first with dummy data fallback pattern
/// 
/// Flow:
/// 1. Try API call first
/// 2. If API fails and useDummyData is true, use dummy data
/// 3. Otherwise, throw the original error
/// 
/// This ensures consistent behavior across all data fetching operations
class ApiServiceWrapper {
  final ApiService _apiService;
  
  // Singleton pattern
  static final ApiServiceWrapper _instance = ApiServiceWrapper._internal();
  factory ApiServiceWrapper() => _instance;
  ApiServiceWrapper._internal() : _apiService = ApiService();
  
  /// Generic method to handle API calls with dummy data fallback
  Future<Map<String, dynamic>> _executeWithFallback<T>({
    required String operation,
    required Future<Map<String, dynamic>> Function() apiCall,
    required Map<String, dynamic> Function() dummyDataCall,
    int fallbackDelay = 500,
  }) async {
    try {
      // Check if we should use dummy data directly
      if (DummyDataService.useDummyData) {
        DummyDataService.logDummyDataUsage(operation);
        await DummyDataService.simulateNetworkDelay();
        return dummyDataCall();
      }

      // Attempt real API call
      debugPrint('ApiServiceWrapper: Attempting $operation via API');
      final response = await apiCall();
      debugPrint('ApiServiceWrapper: $operation API call successful');
      return response;
      
    } catch (e) {
      debugPrint('ApiServiceWrapper: $operation API failed: $e');
      
      // Check if we should fallback to dummy data
      if (DummyDataService.useDummyData) {
        debugPrint('ApiServiceWrapper: Falling back to dummy data for $operation');
        DummyDataService.setApiConnectionStatus(true);
        DummyDataService.logDummyDataUsage('$operation (FALLBACK)');
        await DummyDataService.simulateNetworkDelay(fallbackDelay);
        return dummyDataCall();
      }
      
      // If dummy data is not enabled, rethrow the original error
      debugPrint('ApiServiceWrapper: No fallback available for $operation, rethrowing error');
      rethrow;
    }
  }

  // ======== SUBJECTS API ========
  
  /// Fetch all subjects with fallback to dummy data
  Future<Map<String, dynamic>> fetchSubjects({
    Map<String, dynamic>? queryParams,
    String? token,
  }) async {
    return _executeWithFallback(
      operation: 'FETCH_SUBJECTS',
      apiCall: () => _apiService.get(
        '/subjects',
        queryParams: queryParams,
        token: token,
      ),
      dummyDataCall: () => DummyDataService.getSubjectsResponse(),
    );
  }

  /// Fetch subject by ID with fallback to dummy data
  Future<Map<String, dynamic>> fetchSubjectById({
    required int subjectId,
    String? token,
  }) async {
    return _executeWithFallback(
      operation: 'FETCH_SUBJECT_BY_ID',
      apiCall: () => _apiService.get(
        '/subjects/$subjectId',
        token: token,
      ),
      dummyDataCall: () {
        final subjects = DummyDataService.subjects;
        final subject = subjects.firstWhere(
          (s) => s['id'] == subjectId,
          orElse: () => subjects.first,
        );
        return {
          'success': true,
          'data': subject,
          'message': 'Subject fetched successfully (dummy data)',
        };
      },
    );
  }

  // ======== PACKAGES API ========
  
  /// Fetch all packages with fallback to dummy data
  Future<Map<String, dynamic>> fetchPackages({
    Map<String, dynamic>? queryParams,
    String? token,
  }) async {
    return _executeWithFallback(
      operation: 'FETCH_PACKAGES',
      apiCall: () => _apiService.get(
        '/packages',
        queryParams: queryParams,
        token: token,
      ),
      dummyDataCall: () => DummyDataService.getPackagesResponse(),
    );
  }

  // ======== LESSONS API ========
  
  /// Fetch lessons with optional subject filter and fallback to dummy data
  Future<Map<String, dynamic>> fetchLessons({
    int? subjectId,
    Map<String, dynamic>? queryParams,
    String? token,
  }) async {
    final params = <String, dynamic>{
      ...?queryParams,
      if (subjectId != null) 'subject_id': subjectId,
    };
    
    return _executeWithFallback(
      operation: 'FETCH_LESSONS',
      apiCall: () => _apiService.get(
        '/lessons',
        queryParams: params.isNotEmpty ? params : null,
        token: token,
      ),
      dummyDataCall: () => DummyDataService.getLessonsResponse(subjectId),
    );
  }

  /// Fetch lesson by ID with fallback to dummy data
  Future<Map<String, dynamic>> fetchLessonById({
    required int lessonId,
    String? token,
  }) async {
    return _executeWithFallback(
      operation: 'FETCH_LESSON_BY_ID',
      apiCall: () => _apiService.get(
        '/lessons/$lessonId',
        token: token,
      ),
      dummyDataCall: () => DummyDataService.getLearningResourcesResponse(lessonId),
    );
  }

  // ======== DIGITAL ASSETS API ========
  
  /// Fetch digital assets with fallback to dummy data
  Future<Map<String, dynamic>> fetchDigitalAssets({
    Map<String, dynamic>? queryParams,
    String? token,
  }) async {
    return _executeWithFallback(
      operation: 'FETCH_DIGITAL_ASSETS',
      apiCall: () => _apiService.get(
        '/digital-assets',
        queryParams: queryParams,
        token: token,
      ),
      dummyDataCall: () => DummyDataService.getDigitalAssetsResponse(),
    );
  }

  // ======== AUTHENTICATION API ========
  
  /// Login with fallback (Note: Auth usually shouldn't fallback to dummy data in production)
  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('ApiServiceWrapper: Attempting login via API');
      final response = await _apiService.post(
        '/login',
        bodyData: {
          'email': email,
          'password': password,
        },
      );
      debugPrint('ApiServiceWrapper: Login API call successful');
      return response;
    } catch (e) {
      debugPrint('ApiServiceWrapper: Login API failed: $e');
      
      // For auth, we typically don't want to fallback to dummy data in production
      // But for demo/testing purposes, we can provide a dummy response
      if (DummyDataService.useDummyData && kDebugMode) {
        debugPrint('ApiServiceWrapper: Using dummy login response for demo');
        DummyDataService.logDummyDataUsage('LOGIN (DEMO)');
        await DummyDataService.simulateNetworkDelay(1500);
        
        return {
          'success': true,
          'data': {
            'user': {
              'id': 1,
              'name': 'Demo User',
              'email': email,
              'phone': '+1234567890',
            },
            'token': 'demo_token_${DateTime.now().millisecondsSinceEpoch}',
          },
          'message': 'Login successful (demo mode)',
        };
      }
      
      rethrow;
    }
  }

  // ======== UTILITY METHODS ========
  
  /// Check API connection status
  Future<bool> checkApiConnection() async {
    try {
      // Try a simple endpoint to check connectivity
      await _apiService.get('/subjects', queryParams: {'limit': 1});
      DummyDataService.setApiConnectionStatus(false);
      return true;
    } catch (e) {
      DummyDataService.setApiConnectionStatus(true);
      return false;
    }
  }
  
  /// Get current service status
  Map<String, dynamic> getServiceStatus() {
    return {
      'useDummyData': DummyDataService.useDummyData,
      'dummyDataStatus': DummyDataService.getStatus(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
