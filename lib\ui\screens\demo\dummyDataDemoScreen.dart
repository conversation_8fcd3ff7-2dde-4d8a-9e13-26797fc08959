import 'package:flutter/material.dart';
import 'package:eschool/data/services/dummy_data_service.dart';
import 'package:eschool/data/repositories/subjectRepository.dart';
import 'package:eschool/data/repositories/chatRepository.dart';
import 'package:eschool/ui/widgets/customAppBar.dart';
import 'package:eschool/ui/widgets/customCircularProgressIndicator.dart';

class DummyDataDemoScreen extends StatefulWidget {
  const DummyDataDemoScreen({super.key});

  @override
  State<DummyDataDemoScreen> createState() => _DummyDataDemoScreenState();

  static Route route(RouteSettings routeSettings) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, _) => const DummyDataDemoScreen(),
      transitionDuration: const Duration(milliseconds: 300),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: animation.drive(
            Tween(begin: const Offset(1.0, 0.0), end: Offset.zero),
          ),
          child: child,
        );
      },
    );
  }
}

class _DummyDataDemoScreenState extends State<DummyDataDemoScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final SubjectRepository _subjectRepository = SubjectRepository();
  final ChatRepository _chatRepository = ChatRepository();

  bool _isLoading = false;
  String _statusMessage = 'Ready to test dummy data';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Force dummy data mode for demonstration
    DummyDataService.setApiConnectionStatus(true);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _testSubjects() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Testing subjects API...';
    });

    try {
      final response = await _subjectRepository.fetchStudentSubjects();
      setState(() {
        _statusMessage = 'Subjects loaded: ${response['data'].length} subjects found';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading subjects: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testLessons() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Testing lessons API...';
    });

    try {
      final response = await _subjectRepository.fetchSubjectLessons(subjectId: 1);
      setState(() {
        _statusMessage = 'Lessons loaded: ${response['data'].length} lessons found for subject 1';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading lessons: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testStudyMaterials() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Testing study materials API...';
    });

    try {
      final response = await _subjectRepository.fetchStudyMaterials(lessonId: 1);
      setState(() {
        _statusMessage = 'Study materials loaded: ${response['data'].length} resources found for lesson 1';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading study materials: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testChatUsers() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Testing chat users API...';
    });

    try {
      final response = await _chatRepository.fetchChatUsers(
        offset: 0,
        isParent: false,
      );
      setState(() {
        _statusMessage = 'Chat users loaded: ${response['chatUsers'].length} users found';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading chat users: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testChatMessages() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Testing chat messages API...';
    });

    try {
      final response = await _chatRepository.fetchChatMessages(
        offset: 0,
        chatUserId: "1",
        isParent: false,
      );
      setState(() {
        _statusMessage = 'Chat messages loaded: ${response['chatMessages'].length} messages found for user 1';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading chat messages: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testSendMessage() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Testing send message API...';
    });

    try {
      final response = await _chatRepository.sendChatMessage(
        receiverId: 1,
        message: "Test message from dummy data demo at ${DateTime.now()}",
        filePaths: [],
        isParent: false,
      );
      setState(() {
        _statusMessage = 'Message sent successfully! ID: ${response.id}';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error sending message: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildTestButton({
    required String title,
    required String description,
    required VoidCallback onPressed,
    required IconData icon,
  }) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: ListTile(
        leading: Icon(icon, color: Theme.of(context).primaryColor),
        title: Text(title),
        subtitle: Text(description),
        trailing: const Icon(Icons.play_arrow),
        onTap: _isLoading ? null : onPressed,
      ),
    );
  }

  Widget _buildSubjectsTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        _buildTestButton(
          title: 'Test Subjects',
          description: 'Load all available subjects',
          icon: Icons.book,
          onPressed: _testSubjects,
        ),
        _buildTestButton(
          title: 'Test Lessons',
          description: 'Load lessons for Mathematics subject',
          icon: Icons.list,
          onPressed: _testLessons,
        ),
        _buildTestButton(
          title: 'Test Study Materials',
          description: 'Load resources for first lesson',
          icon: Icons.description,
          onPressed: _testStudyMaterials,
        ),
        const SizedBox(height: 16),
        // Show sample data
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Sample Subject Data',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ...DummyDataService.subjects.take(3).map(
                  (subject) => ListTile(
                    leading: const Icon(Icons.book),
                    title: Text(subject['name'] ?? subject['title']),
                    subtitle: Text(subject['code'] ?? ''),
                    dense: true,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLessonsTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Sample Lessons (Mathematics)',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ...DummyDataService.lessons.take(3).map(
                  (lesson) => ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                      child: Text(lesson['id'].toString()),
                    ),
                    title: Text(lesson['name']),
                    subtitle: Text(lesson['description']),
                    trailing: Chip(
                      label: Text('Lesson'),
                      backgroundColor: Colors.blue.withValues(alpha: 0.1),
                    ),
                    dense: true,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResourcesTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Sample Learning Resources',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                // Show files from the first lesson
                if (DummyDataService.lessons.isNotEmpty && DummyDataService.lessons.first['file'] != null)
                  ...DummyDataService.lessons.first['file'].map<Widget>(
                    (resource) => ListTile(
                      leading: Icon(
                        resource['type'] == '1'
                            ? Icons.picture_as_pdf
                            : resource['type'] == '2'
                                ? Icons.play_circle
                                : Icons.videocam,
                        color: resource['type'] == '1'
                            ? Colors.red
                            : resource['type'] == '2'
                                ? Colors.blue
                                : Colors.green,
                      ),
                      title: Text(resource['file_name'] ?? 'Resource'),
                      subtitle: Text('${resource['file_extension']?.toUpperCase() ?? 'FILE'}'),
                      trailing: const Icon(Icons.download),
                      dense: true,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChatTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        _buildTestButton(
          title: 'Test Chat Users',
          description: 'Load all chat users/teachers',
          icon: Icons.people,
          onPressed: _testChatUsers,
        ),
        _buildTestButton(
          title: 'Test Chat Messages',
          description: 'Load messages with first teacher',
          icon: Icons.message,
          onPressed: _testChatMessages,
        ),
        _buildTestButton(
          title: 'Test Send Message',
          description: 'Send a test message',
          icon: Icons.send,
          onPressed: _testSendMessage,
        ),
        const SizedBox(height: 16),
        // Show sample chat users
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Sample Chat Users',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                ...DummyDataService.getChatUsersResponse()['data']['items'].take(4).map(
                  (user) => ListTile(
                    leading: CircleAvatar(
                      backgroundImage: NetworkImage(user['image']),
                    ),
                    title: Text(user['name']),
                    subtitle: Text(user['email']),
                    trailing: user['unread_count'] > 0
                        ? CircleAvatar(
                            radius: 10,
                            backgroundColor: Colors.red,
                            child: Text(
                              user['unread_count'].toString(),
                              style: const TextStyle(color: Colors.white, fontSize: 12),
                            ),
                          )
                        : null,
                    dense: true,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Dummy Data Demo',
        actionButton: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            if (value == 'toggle') {
              // Toggle dummy data mode
              DummyDataService.setApiConnectionStatus(!DummyDataService.useDummyData);
              setState(() {
                _statusMessage = DummyDataService.useDummyData
                    ? 'Switched to DUMMY DATA mode'
                    : 'Switched to REAL API mode';
              });
            }
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'toggle',
              child: Row(
                children: [
                  Icon(DummyDataService.useDummyData ? Icons.cloud : Icons.cloud_off),
                  const SizedBox(width: 8),
                  Text(DummyDataService.useDummyData ? 'Use Real API' : 'Use Dummy Data'),
                ],
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Tabs
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Subjects', icon: Icon(Icons.book)),
              Tab(text: 'Lessons', icon: Icon(Icons.list)),
              Tab(text: 'Resources', icon: Icon(Icons.description)),
              Tab(text: 'Chat', icon: Icon(Icons.message)),
            ],
          ),
          // Status container
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            color: DummyDataService.useDummyData
                ? Colors.orange.withValues(alpha: 0.1)
                : Colors.green.withValues(alpha: 0.1),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      DummyDataService.useDummyData ? Icons.warning : Icons.check_circle,
                      color: DummyDataService.useDummyData ? Colors.orange : Colors.green,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      DummyDataService.useDummyData
                          ? 'DUMMY DATA MODE ACTIVE'
                          : 'REAL API MODE',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: DummyDataService.useDummyData ? Colors.orange : Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                if (_isLoading) ...[
                  const CustomCircularProgressIndicator(),
                  const SizedBox(height: 8),
                ],
                Text(
                  _statusMessage,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSubjectsTab(),
                _buildLessonsTab(),
                _buildResourcesTab(),
                _buildChatTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // Toggle dummy data mode
          DummyDataService.setApiConnectionStatus(!DummyDataService.useDummyData);
          setState(() {
            _statusMessage = DummyDataService.useDummyData
                ? 'Switched to DUMMY DATA mode'
                : 'Switched to REAL API mode';
          });
        },
        icon: Icon(DummyDataService.useDummyData ? Icons.cloud : Icons.cloud_off),
        label: Text(DummyDataService.useDummyData ? 'Use Real API' : 'Use Dummy Data'),
      ),
    );
  }
}