import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../tokens/colors.dart';
import '../../tokens/spacing.dart';
import '../../tokens/typography.dart';

/// Shared form text field widget following the design system
class BottomSheetTextField extends StatelessWidget {
  const BottomSheetTextField({
    super.key,
    required this.hintText,
    required this.textEditingController,
    this.maxLines = 1,
    this.height,
    this.textInputFormatter,
    this.suffix,
    this.prefix,
    this.maxLength,
    this.hideText,
    this.keyboardType,
    this.margin,
    this.contentAlignment,
    this.contentPadding,
    this.disabled = false,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.borderWidth = 1,
    this.fillColor,
    this.textStyle,
    this.hintStyle,
    this.errorText,
    this.labelText,
    this.helperText,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.focusNode,
    this.autofocus = false,
    this.readOnly = false,
    this.textCapitalization = TextCapitalization.none,
    this.textInputAction,
    this.validator,
    this.autovalidateMode,
  });

  /// Hint text displayed when field is empty
  final String hintText;

  /// Text editing controller
  final TextEditingController textEditingController;

  /// Container alignment
  final AlignmentGeometry? contentAlignment;

  /// Content padding inside the field
  final EdgeInsetsGeometry? contentPadding;

  /// Container height
  final double? height;

  /// Maximum number of lines
  final int? maxLines;

  /// Text input formatters
  final List<TextInputFormatter>? textInputFormatter;

  /// Keyboard type
  final TextInputType? keyboardType;

  /// External margin
  final EdgeInsetsGeometry? margin;

  /// Whether text should be hidden (password)
  final bool? hideText;

  /// Suffix widget (e.g., icons)
  final Widget? suffix;

  /// Prefix widget
  final Widget? prefix;

  /// Maximum character length
  final int? maxLength;

  /// Whether the field is disabled
  final bool disabled;

  /// Background color of the container
  final Color? backgroundColor;

  /// Border color
  final Color? borderColor;

  /// Border radius
  final double? borderRadius;

  /// Border width
  final double borderWidth;

  /// Fill color (background of input area)
  final Color? fillColor;

  /// Text style
  final TextStyle? textStyle;

  /// Hint text style
  final TextStyle? hintStyle;

  /// Error text to display
  final String? errorText;

  /// Label text
  final String? labelText;

  /// Helper text
  final String? helperText;

  /// Called when text changes
  final ValueChanged<String>? onChanged;

  /// Called when editing is submitted
  final ValueChanged<String>? onSubmitted;

  /// Called when field is tapped
  final VoidCallback? onTap;

  /// Focus node
  final FocusNode? focusNode;

  /// Whether to autofocus
  final bool autofocus;

  /// Whether field is read-only
  final bool readOnly;

  /// Text capitalization
  final TextCapitalization textCapitalization;

  /// Text input action
  final TextInputAction? textInputAction;

  /// Validator function
  final String? Function(String?)? validator;

  /// Auto-validation mode
  final AutovalidateMode? autovalidateMode;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Determine colors
    final effectiveBackgroundColor = backgroundColor ?? AppColors.surface;
    final effectiveBorderColor = borderColor ?? 
        (errorText != null ? AppColors.error : AppColors.border);
    final effectiveFillColor = fillColor ?? AppColors.surface;

    // Determine text styles
    final effectiveTextStyle = textStyle ?? AppTypography.inputText.copyWith(
      color: disabled ? AppColors.neutral600 : AppColors.onSurface,
    );
    final effectiveHintStyle = hintStyle ?? AppTypography.inputHint;

    return Container(
      margin: margin,
      alignment: contentAlignment ?? Alignment.center,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(borderRadius ?? AppSpacing.inputRadius),
        border: Border.all(
          color: effectiveBorderColor,
          width: borderWidth,
        ),
      ),
      width: double.infinity,
      height: height,
      child: IntrinsicHeight(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Label text
            if (labelText != null) ...[
              Padding(
                padding: const EdgeInsets.only(
                  left: AppSpacing.inputPadding,
                  top: AppSpacing.sm,
                  right: AppSpacing.inputPadding,
                ),
                child: Text(
                  labelText!,
                  style: AppTypography.inputLabel,
                ),
              ),
              const SizedBox(height: AppSpacing.xs),
            ],
            
            // Text field
            Flexible(
              child: TextField(
                enabled: !disabled,
                readOnly: readOnly,
                obscureText: hideText ?? false,
                keyboardType: keyboardType,
                controller: textEditingController,
                style: effectiveTextStyle,
                maxLines: (hideText ?? false) ? 1 : maxLines,
                maxLength: maxLength,
                inputFormatters: textInputFormatter,
                onChanged: onChanged,
                onSubmitted: onSubmitted,
                onTap: onTap,
                focusNode: focusNode,
                autofocus: autofocus,
                textCapitalization: textCapitalization,
                textInputAction: textInputAction,
                decoration: InputDecoration(
                  filled: true,
                  fillColor: effectiveFillColor,
                  prefixIcon: prefix,
                  suffixIcon: suffix,
                  hintText: hintText,
                  hintStyle: effectiveHintStyle,
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  focusedErrorBorder: InputBorder.none,
                  contentPadding: contentPadding ?? const EdgeInsets.all(AppSpacing.inputPadding),
                  counterText: maxLength != null ? null : "",
                ),
              ),
            ),
            
            // Error, helper text
            if (errorText != null || helperText != null) ...[
              Padding(
                padding: const EdgeInsets.only(
                  left: AppSpacing.inputPadding,
                  right: AppSpacing.inputPadding,
                  bottom: AppSpacing.sm,
                  top: AppSpacing.xs,
                ),
                child: Text(
                  errorText ?? helperText!,
                  style: AppTypography.bodySmall.copyWith(
                    color: errorText != null ? AppColors.error : AppColors.onSurfaceVariant,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Form field variants for common use cases
class AppFormField {
  AppFormField._();

  /// Standard text field
  static Widget text({
    Key? key,
    required String hintText,
    required TextEditingController controller,
    String? labelText,
    String? helperText,
    String? errorText,
    Widget? suffix,
    Widget? prefix,
    EdgeInsetsGeometry? margin,
    bool disabled = false,
    ValueChanged<String>? onChanged,
    VoidCallback? onTap,
    int maxLines = 1,
    int? maxLength,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return BottomSheetTextField(
      key: key,
      hintText: hintText,
      textEditingController: controller,
      labelText: labelText,
      helperText: helperText,
      errorText: errorText,
      suffix: suffix,
      prefix: prefix,
      margin: margin ?? const EdgeInsets.only(bottom: AppSpacing.formFieldSpacing),
      disabled: disabled,
      onChanged: onChanged,
      onTap: onTap,
      maxLines: maxLines,
      maxLength: maxLength,
      textInputFormatter: inputFormatters,
    );
  }

  /// Password field with visibility toggle
  static Widget password({
    Key? key,
    required String hintText,
    required TextEditingController controller,
    required bool isPasswordVisible,
    required VoidCallback onVisibilityToggle,
    String? labelText,
    String? helperText,
    String? errorText,
    EdgeInsetsGeometry? margin,
    bool disabled = false,
    ValueChanged<String>? onChanged,
  }) {
    return BottomSheetTextField(
      key: key,
      hintText: hintText,
      textEditingController: controller,
      labelText: labelText,
      helperText: helperText,
      errorText: errorText,
      hideText: !isPasswordVisible,
      suffix: IconButton(
        onPressed: onVisibilityToggle,
        icon: Icon(
          isPasswordVisible ? Icons.visibility_off : Icons.visibility,
          color: AppColors.onSurfaceVariant,
        ),
      ),
      margin: margin ?? const EdgeInsets.only(bottom: AppSpacing.formFieldSpacing),
      disabled: disabled,
      onChanged: onChanged,
    );
  }

  /// Email field
  static Widget email({
    Key? key,
    required String hintText,
    required TextEditingController controller,
    String? labelText,
    String? helperText,
    String? errorText,
    EdgeInsetsGeometry? margin,
    bool disabled = false,
    ValueChanged<String>? onChanged,
  }) {
    return BottomSheetTextField(
      key: key,
      hintText: hintText,
      textEditingController: controller,
      labelText: labelText,
      helperText: helperText,
      errorText: errorText,
      keyboardType: TextInputType.emailAddress,
      suffix: const Icon(Icons.email_outlined, color: AppColors.onSurfaceVariant),
      margin: margin ?? const EdgeInsets.only(bottom: AppSpacing.formFieldSpacing),
      disabled: disabled,
      onChanged: onChanged,
      textInputFormatter: [
        FilteringTextInputFormatter.deny(RegExp(r'\s')), // No spaces
      ],
    );
  }

  /// Phone number field
  static Widget phone({
    Key? key,
    required String hintText,
    required TextEditingController controller,
    String? labelText,
    String? helperText,
    String? errorText,
    EdgeInsetsGeometry? margin,
    bool disabled = false,
    ValueChanged<String>? onChanged,
  }) {
    return BottomSheetTextField(
      key: key,
      hintText: hintText,
      textEditingController: controller,
      labelText: labelText,
      helperText: helperText,
      errorText: errorText,
      keyboardType: TextInputType.phone,
      suffix: const Icon(Icons.phone_outlined, color: AppColors.onSurfaceVariant),
      margin: margin ?? const EdgeInsets.only(bottom: AppSpacing.formFieldSpacing),
      disabled: disabled,
      onChanged: onChanged,
      textInputFormatter: [
        FilteringTextInputFormatter.digitsOnly,
      ],
    );
  }

  /// Number field
  static Widget number({
    Key? key,
    required String hintText,
    required TextEditingController controller,
    String? labelText,
    String? helperText,
    String? errorText,
    EdgeInsetsGeometry? margin,
    bool disabled = false,
    ValueChanged<String>? onChanged,
    bool allowDecimals = false,
  }) {
    return BottomSheetTextField(
      key: key,
      hintText: hintText,
      textEditingController: controller,
      labelText: labelText,
      helperText: helperText,
      errorText: errorText,
      keyboardType: allowDecimals 
          ? const TextInputType.numberWithOptions(decimal: true)
          : TextInputType.number,
      margin: margin ?? const EdgeInsets.only(bottom: AppSpacing.formFieldSpacing),
      disabled: disabled,
      onChanged: onChanged,
      textInputFormatter: allowDecimals
          ? [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$'))]
          : [FilteringTextInputFormatter.digitsOnly],
    );
  }

  /// Multiline text area
  static Widget textArea({
    Key? key,
    required String hintText,
    required TextEditingController controller,
    String? labelText,
    String? helperText,
    String? errorText,
    EdgeInsetsGeometry? margin,
    bool disabled = false,
    ValueChanged<String>? onChanged,
    int maxLines = 3,
    int? maxLength,
  }) {
    return BottomSheetTextField(
      key: key,
      hintText: hintText,
      textEditingController: controller,
      labelText: labelText,
      helperText: helperText,
      errorText: errorText,
      margin: margin ?? const EdgeInsets.only(bottom: AppSpacing.formFieldSpacing),
      disabled: disabled,
      onChanged: onChanged,
      maxLines: maxLines,
      maxLength: maxLength,
      textCapitalization: TextCapitalization.sentences,
    );
  }

  /// Search field with search icon
  static Widget search({
    Key? key,
    required String hintText,
    required TextEditingController controller,
    String? labelText,
    String? helperText,
    EdgeInsetsGeometry? margin,
    bool disabled = false,
    ValueChanged<String>? onChanged,
    VoidCallback? onClear,
  }) {
    return BottomSheetTextField(
      key: key,
      hintText: hintText,
      textEditingController: controller,
      labelText: labelText,
      helperText: helperText,
      prefix: const Icon(Icons.search, color: AppColors.onSurfaceVariant),
      suffix: onClear != null
          ? IconButton(
              onPressed: onClear,
              icon: const Icon(Icons.clear, color: AppColors.onSurfaceVariant),
            )
          : null,
      margin: margin ?? const EdgeInsets.only(bottom: AppSpacing.formFieldSpacing),
      disabled: disabled,
      onChanged: onChanged,
      textInputAction: TextInputAction.search,
    );
  }
} 