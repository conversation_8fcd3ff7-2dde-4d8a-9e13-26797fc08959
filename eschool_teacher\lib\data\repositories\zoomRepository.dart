import 'package:eschool_teacher/data/models/zoom_meeting.dart';
import 'package:eschool_teacher/utils/api.dart';
import 'package:eschool_teacher/utils/dummy_data_utils.dart';
import 'package:eschool_teacher/utils/errorMessageKeys.dart';
import 'package:flutter/material.dart';

class ZoomRepository {
  // Create a new Zoom meeting
  Future<Map<String, dynamic>> createZoomMeeting({
    required String lessonId,
    required DateTime startTime,
    required DateTime endTime,
    required String topic,
  }) async {
    try {
      final response = await Api.post(
        url: Api.createZoomMeeting,
        useAuthToken: true,
        body: {
          "lesson_id": lessonId,
          "start_time": startTime.toIso8601String(),
          "end_time": endTime.toIso8601String(),
          "topic": topic,
        },
      );

      if (response['status']) {
        return {
          "meeting": ZoomMeeting.fromJson(response['data']),
          "error": false,
        };
      }
      
      // If API fails, generate a dummy meeting
      debugPrint("Zoom API not finalized, creating dummy meeting");
      final dummyMeeting = ZoomMeeting(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        meetingId: "9${DateTime.now().millisecondsSinceEpoch.toString().substring(5, 13)}",
        password: "pwd${DateTime.now().millisecondsSinceEpoch.toString().substring(10, 13)}",
        joinUrl: "https://zoom.us/j/9${DateTime.now().millisecondsSinceEpoch.toString().substring(5, 13)}",
        startTime: startTime,
        endTime: endTime,
        lessonId: lessonId,
      );
      
      return {
        "meeting": dummyMeeting,
        "error": false,
      };
    } catch (e) {
      debugPrint("Error createZoomMeeting: $e");
      // Generate dummy meeting on error
      final dummyMeeting = ZoomMeeting(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        meetingId: "9${DateTime.now().millisecondsSinceEpoch.toString().substring(5, 13)}",
        password: "pwd${DateTime.now().millisecondsSinceEpoch.toString().substring(10, 13)}",
        joinUrl: "https://zoom.us/j/9${DateTime.now().millisecondsSinceEpoch.toString().substring(5, 13)}",
        startTime: startTime,
        endTime: endTime,
        lessonId: lessonId,
      );
      
      return {
        "meeting": dummyMeeting,
        "error": false,
      };
    }
  }

  // Update an existing Zoom meeting
  Future<Map<String, dynamic>> updateZoomMeeting({
    required String meetingId,
    required DateTime startTime,
    required DateTime endTime,
    String? topic,
  }) async {
    try {
      Map<String, dynamic> body = {
        "meeting_id": meetingId,
        "start_time": startTime.toIso8601String(),
        "end_time": endTime.toIso8601String(),
      };
      
      if (topic != null) {
        body["topic"] = topic;
      }
      
      final response = await Api.post(
        url: Api.updateZoomMeeting,
        useAuthToken: true,
        body: body,
      );

      if (response['status']) {
        return {
          "meeting": ZoomMeeting.fromJson(response['data']),
          "error": false,
        };
      }
      
      // If API fails, return a dummy updated meeting
      debugPrint("Zoom API not finalized, returning dummy updated meeting");
      final dummyMeeting = ZoomMeeting(
        id: meetingId,
        meetingId: meetingId,
        password: "password123",
        joinUrl: "https://zoom.us/j/$meetingId",
        startTime: startTime,
        endTime: endTime,
        lessonId: "unknown",
      );
      
      return {
        "meeting": dummyMeeting,
        "error": false,
      };
    } catch (e) {
      debugPrint("Error updateZoomMeeting: $e");
      // Return dummy meeting on error
      final dummyMeeting = ZoomMeeting(
        id: meetingId,
        meetingId: meetingId,
        password: "password123",
        joinUrl: "https://zoom.us/j/$meetingId",
        startTime: startTime,
        endTime: endTime,
        lessonId: "unknown",
      );
      
      return {
        "meeting": dummyMeeting,
        "error": false,
      };
    }
  }

  // Delete a Zoom meeting
  Future<Map<String, dynamic>> deleteZoomMeeting({
    required String meetingId,
  }) async {
    try {
      final response = await Api.post(
        url: Api.deleteZoomMeeting,
        useAuthToken: true,
        body: {
          "meeting_id": meetingId,
        },
      );

      if (response['status']) {
        return {
          "success": true,
          "error": false,
        };
      }
      
      // If API fails, simulate success
      debugPrint("Zoom API not finalized, simulating successful deletion");
      return {
        "success": true,
        "error": false,
      };
    } catch (e) {
      debugPrint("Error deleteZoomMeeting: $e");
      // Simulate success on error
      return {
        "success": true,
        "error": false,
      };
    }
  }

  // Get Zoom meetings for a lesson or all lessons
  Future<Map<String, dynamic>> getZoomMeetings({
    String? lessonId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Map<String, dynamic> queryParams = {};
      if (lessonId != null) queryParams["lesson_id"] = lessonId;
      if (startDate != null) queryParams["start_date"] = startDate.toIso8601String();
      if (endDate != null) queryParams["end_date"] = endDate.toIso8601String();
      
      final response = await Api.get(
        url: Api.getZoomMeetings,
        useAuthToken: true,
        queryParameters: queryParams,
      );

      if (response['status']) {
        final List<ZoomMeeting> meetings = (response['data']['meetings'] as List)
            .map((meeting) => ZoomMeeting.fromJson(meeting))
            .toList();

        return {
          "meetings": meetings,
          "error": false,
        };
      }
      
      // If API fails, return dummy meetings
      debugPrint("Zoom API not finalized, returning dummy meetings");
      return {
        "meetings": DummyDataUtils.getDummyZoomMeetings(),
        "error": false,
      };
    } catch (e) {
      debugPrint("Error getZoomMeetings: $e");
      // Return dummy meetings on error
      return {
        "meetings": DummyDataUtils.getDummyZoomMeetings(),
        "error": false,
      };
    }
  }
} 