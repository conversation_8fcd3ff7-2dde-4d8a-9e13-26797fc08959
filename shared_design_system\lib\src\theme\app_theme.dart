import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../tokens/colors.dart';
import '../tokens/typography.dart';
import '../tokens/spacing.dart';
import '../tokens/shadows.dart';

/// Main theme configuration for eSchool apps
class AppTheme {
  AppTheme._();

  /// Light theme configuration (only theme for the apps)
  static ThemeData get light => ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    
    // Color scheme
    colorScheme: AppColorScheme.light,
    
    // Typography
    textTheme: AppTextTheme.textTheme,
    fontFamily: AppTypography.fontFamily,
    
    // App bar theme
    appBarTheme: _appBarTheme,
    
    // Bottom navigation bar theme
    bottomNavigationBarTheme: _bottomNavigationBarTheme,
    
    // Card theme
    cardTheme: _cardTheme,
    
    // Elevated button theme
    elevatedButtonTheme: _elevatedButtonTheme,
    
    // Text button theme
    textButtonTheme: _textButtonTheme,
    
    // Outlined button theme
    outlinedButtonTheme: _outlinedButtonTheme,
    
    // Input decoration theme
    inputDecorationTheme: _inputDecorationTheme,
    
    // Dialog theme
    dialogTheme: _dialogTheme,
    
    // Bottom sheet theme
    bottomSheetTheme: _bottomSheetTheme,
    
    // Floating action button theme
    floatingActionButtonTheme: _floatingActionButtonTheme,
    
    // Divider theme
    dividerTheme: _dividerTheme,
    
    // List tile theme
    listTileTheme: _listTileTheme,
    
    // Chip theme
    chipTheme: _chipTheme,
    
    // Tab bar theme
    tabBarTheme: _tabBarTheme,
    
    // System UI overlay style
    visualDensity: VisualDensity.adaptivePlatformDensity,
  );

  // App bar theme configuration
  static const AppBarTheme _appBarTheme = AppBarTheme(
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.onPrimary,
    elevation: AppElevation.appBar,
    shadowColor: AppColors.shadow,
    centerTitle: true,
    titleTextStyle: AppTypography.appBarTitle,
    toolbarHeight: AppSpacing.appBarHeight,
    systemOverlayStyle: SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
    ),
  );

  // Bottom navigation bar theme
  static const BottomNavigationBarThemeData _bottomNavigationBarTheme = BottomNavigationBarThemeData(
    backgroundColor: AppColors.surface,
    selectedItemColor: AppColors.primary,
    unselectedItemColor: AppColors.neutral600,
    type: BottomNavigationBarType.fixed,
    elevation: AppElevation.bottomNavigationBar,
    selectedLabelStyle: AppTypography.labelSmall,
    unselectedLabelStyle: AppTypography.labelSmall,
  );

  // Card theme configuration
  static CardTheme get _cardTheme => CardTheme(
    color: AppColors.surface,
    shadowColor: AppColors.shadow,
    elevation: AppElevation.card,
    margin: const EdgeInsets.all(AppSpacing.sm),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppSpacing.cardRadius),
    ),
  );

  // Elevated button theme
  static ElevatedButtonThemeData get _elevatedButtonTheme => ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
      elevation: AppElevation.button,
      shadowColor: AppColors.shadow,
      textStyle: AppTypography.buttonText,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.buttonPadding,
        vertical: AppSpacing.md,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.buttonRadius),
      ),
      minimumSize: const Size(88, AppSpacing.minTouchTarget),
    ),
  );

  // Text button theme
  static TextButtonThemeData get _textButtonTheme => TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.primary,
      textStyle: AppTypography.buttonText,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.buttonPadding,
        vertical: AppSpacing.md,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.buttonRadius),
      ),
      minimumSize: const Size(88, AppSpacing.minTouchTarget),
    ),
  );

  // Outlined button theme
  static OutlinedButtonThemeData get _outlinedButtonTheme => OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      foregroundColor: AppColors.primary,
      textStyle: AppTypography.buttonText,
      side: const BorderSide(color: AppColors.primary),
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.buttonPadding,
        vertical: AppSpacing.md,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSpacing.buttonRadius),
      ),
      minimumSize: const Size(88, AppSpacing.minTouchTarget),
    ),
  );

  // Input decoration theme
  static InputDecorationTheme get _inputDecorationTheme => InputDecorationTheme(
    filled: true,
    fillColor: AppColors.surface,
    contentPadding: const EdgeInsets.all(AppSpacing.inputPadding),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppSpacing.inputRadius),
      borderSide: const BorderSide(color: AppColors.border),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppSpacing.inputRadius),
      borderSide: const BorderSide(color: AppColors.border),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppSpacing.inputRadius),
      borderSide: const BorderSide(color: AppColors.primary, width: 2),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppSpacing.inputRadius),
      borderSide: const BorderSide(color: AppColors.error),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppSpacing.inputRadius),
      borderSide: const BorderSide(color: AppColors.error, width: 2),
    ),
    labelStyle: AppTypography.inputLabel,
    hintStyle: AppTypography.inputHint,
    helperStyle: AppTypography.bodySmall,
    errorStyle: AppTypography.bodySmall.copyWith(color: AppColors.error),
  );

  // Dialog theme
  static DialogTheme get _dialogTheme => DialogTheme(
    backgroundColor: AppColors.surface,
    elevation: AppElevation.dialog,
    shadowColor: AppColors.shadow,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppSpacing.dialogRadius),
    ),
    titleTextStyle: AppTypography.headlineSmall,
    contentTextStyle: AppTypography.bodyMedium,
  );

  // Bottom sheet theme
  static const BottomSheetThemeData _bottomSheetTheme = BottomSheetThemeData(
    backgroundColor: AppColors.surface,
    elevation: AppElevation.bottomSheet,
    shadowColor: AppColors.shadow,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(AppSpacing.bottomSheetRadius),
        topRight: Radius.circular(AppSpacing.bottomSheetRadius),
      ),
    ),
  );

  // Floating action button theme
  static const FloatingActionButtonThemeData _floatingActionButtonTheme = FloatingActionButtonThemeData(
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.onPrimary,
    elevation: AppElevation.floatingActionButton,
  );

  // Divider theme
  static const DividerThemeData _dividerTheme = DividerThemeData(
    color: AppColors.border,
    thickness: 1,
    space: 1,
  );

  // List tile theme
  static const ListTileThemeData _listTileTheme = ListTileThemeData(
    contentPadding: EdgeInsets.symmetric(
      horizontal: AppSpacing.screenPadding,
      vertical: AppSpacing.sm,
    ),
    titleTextStyle: AppTypography.titleMedium,
    subtitleTextStyle: AppTypography.bodySmall,
    leadingAndTrailingTextStyle: AppTypography.labelMedium,
  );

  // Chip theme
  static ChipThemeData get _chipTheme => ChipThemeData(
    backgroundColor: AppColors.surfaceVariant,
    disabledColor: AppColors.neutral300,
    selectedColor: AppColors.primary,
    secondarySelectedColor: AppColors.primaryLight,
    shadowColor: AppColors.shadow,
    selectedShadowColor: AppColors.shadow,
    labelStyle: AppTypography.labelMedium,
    secondaryLabelStyle: AppTypography.labelMedium.copyWith(color: AppColors.onPrimary),
    brightness: Brightness.light,
    elevation: 0,
    pressElevation: AppElevation.button,
    padding: const EdgeInsets.symmetric(
      horizontal: AppSpacing.md,
      vertical: AppSpacing.sm,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppSpacing.radiusRound),
    ),
  );

  // Tab bar theme
  static const TabBarTheme _tabBarTheme = TabBarTheme(
    labelColor: AppColors.primary,
    unselectedLabelColor: AppColors.neutral600,
    labelStyle: AppTypography.labelLarge,
    unselectedLabelStyle: AppTypography.labelLarge,
    indicator: UnderlineTabIndicator(
      borderSide: BorderSide(color: AppColors.primary, width: 2),
    ),
    indicatorSize: TabBarIndicatorSize.tab,
  );
}

/// System UI overlay styles for different contexts
class AppSystemUIOverlayStyles {
  AppSystemUIOverlayStyles._();

  // Light status bar (for dark app bars)
  static const SystemUiOverlayStyle light = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.light,
    statusBarBrightness: Brightness.dark,
    systemNavigationBarColor: AppColors.surface,
    systemNavigationBarIconBrightness: Brightness.dark,
  );

  // Dark status bar (for light app bars)
  static const SystemUiOverlayStyle dark = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.light,
    systemNavigationBarColor: AppColors.surface,
    systemNavigationBarIconBrightness: Brightness.dark,
  );

  // Transparent (for full-screen content)
  static const SystemUiOverlayStyle transparent = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    systemNavigationBarColor: Colors.transparent,
  );
} 