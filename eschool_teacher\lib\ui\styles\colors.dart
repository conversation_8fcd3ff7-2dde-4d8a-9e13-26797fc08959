import 'package:flutter/material.dart';

// Main theme colors based on PRD requirements
const Color primaryColor = Color(0xFF5F97B7);  // Primary brand color
const Color highlightColor = Color(0xFF25536d); // Darker highlight color
const Color backgroundColor = Color(0xFFe9e9e9); // Light background color

// Additional UI colors
const Color secondaryColor = Color(0xFFF4C430); // Yellow accent color
const Color pageBackgroundColor = backgroundColor;
const Color errorColor = Color(0xFFd32f2f);
const Color successColor = Color(0xFF388e3c);
const Color warningColor = Color(0xFFf57c00);

// Text colors
const Color onPrimaryColor = Colors.white;
const Color onSecondaryColor = Colors.black;
const Color onSurfaceColor = Colors.black87;
const Color bodyTextColor = Color(0xFF666666);
const Color hintTextColor = Color(0xFF999999);

// Component colors
const Color surfaceColor = Colors.white;
const Color cardColor = Colors.white;
const Color dividerColor = Color(0xFFEEEEEE);
const Color greenColor = Color(0xFF388e3c);
const Color redColor = errorColor;
const Color yellowColor = Color(0xFFFFD700);
const Color blueColor = primaryColor;
const Color iconColor = Color(0xFF757575);

// Status colors
const Color pendingStatusColor = Color(0xFFf57c00);
const Color approvedStatusColor = Color(0xFF388e3c);
const Color rejectedStatusColor = Color(0xFFd32f2f);
const Color completeStatusColor = Color(0xFF1565c0);
const Color inProgressStatusColor = Color(0xFF9575cd);

//classes colors
const List<Color> myClassesColors = [
  Color(0xff65a3fe),
  Color(0xffff6769),
  Color(0xfffdb46c),
  Color(0xff3bcc97),
];

const Color assignmentViewButtonColor = Color(0xff65a3fe);
const Color assignmentDownloadButtonColor = Color(0xfffdb46c);

//shimmer loading colors
final Color shimmerBaseColor = Colors.grey.shade300;
final Color shimmerhighlightColor = Colors.grey.shade100;
final Color shimmerContentColor = Colors.white.withValues(alpha: 0.85);

//notification count red color, also used in showing statuses
const Color orangeColor = Colors.orange;
