import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:eschool/data/services/local_app_settings_service.dart';

abstract class LocalAppSettingsState {}

class LocalAppSettingsInitial extends LocalAppSettingsState {}

class LocalAppSettingsLoaded extends LocalAppSettingsState {
  final Map<String, dynamic> settings;
  
  LocalAppSettingsLoaded(this.settings);
}

class LocalAppSettingsError extends LocalAppSettingsState {
  final String errorMessage;
  
  LocalAppSettingsError(this.errorMessage);
}

/// Cubit for managing local app settings state
class LocalAppSettingsCubit extends Cubit<LocalAppSettingsState> {
  final LocalAppSettingsService _localSettingsService;

  LocalAppSettingsCubit() 
      : _localSettingsService = LocalAppSettingsService.instance,
        super(LocalAppSettingsInitial());

  /// Load app settings from local service
  void loadAppSettings() {
    try {
      debugPrint('LocalAppSettingsCubit: Loading local app settings');
      final settingsResponse = _localSettingsService.getAppSettings();
      final settingsData = settingsResponse['data'] as Map<String, dynamic>;
      
      emit(LocalAppSettingsLoaded(settingsData));
      debugPrint('LocalAppSettingsCubit: App settings loaded successfully');
    } catch (e) {
      debugPrint('LocalAppSettingsCubit: Error loading app settings: $e');
      emit(LocalAppSettingsError(e.toString()));
    }
  }

  /// Get current settings data (returns null if not loaded)
  Map<String, dynamic>? get currentSettings {
    final currentState = state;
    if (currentState is LocalAppSettingsLoaded) {
      return currentState.settings;
    }
    return null;
  }

  /// Get specific setting value by key path
  dynamic getSetting(String keyPath) {
    final settings = currentSettings;
    if (settings == null) return null;
    
    return _localSettingsService.getSetting(keyPath);
  }

  /// Convenience getters for commonly used settings
  bool get isDemoMode => _localSettingsService.isDemoMode;
  String get schoolName => _localSettingsService.schoolName;
  String get schoolTagline => _localSettingsService.schoolTagline;
  String get currencyCode => _localSettingsService.currencyCode;
  String get currencySymbol => _localSettingsService.currencySymbol;
  bool get isOnlinePaymentEnabled => _localSettingsService.isOnlinePaymentEnabled;
  String get appVersion => _localSettingsService.appVersion;
  String get iosAppVersion => _localSettingsService.iosAppVersion;
  bool get isForceUpdateRequired => _localSettingsService.isForceUpdateRequired;
  bool get isAppUnderMaintenance => _localSettingsService.isAppUnderMaintenance;
  List<String> get holidayDays => _localSettingsService.holidayDays;

  /// Get session year information
  Map<String, dynamic>? get sessionYear {
    return getSetting('session_year') as Map<String, dynamic>?;
  }

  /// Get payment options
  Map<String, dynamic>? get paymentOptions {
    return getSetting('payment_options') as Map<String, dynamic>?;
  }

  /// Get chat settings
  Map<String, dynamic>? get chatSettings {
    return getSetting('chat_settings') as Map<String, dynamic>?;
  }

  /// Get current semester information
  Map<String, dynamic>? get currentSemester {
    return getSetting('current_semester') as Map<String, dynamic>?;
  }

  /// Check if Stripe payment is enabled
  bool get isStripeEnabled {
    final paymentOpts = paymentOptions;
    if (paymentOpts == null) return false;
    
    final stripe = paymentOpts['stripe'] as Map<String, dynamic>?;
    return stripe?['stripe_status'] == '1';
  }

  /// Get Stripe publishable key
  String? get stripePublishableKey {
    final paymentOpts = paymentOptions;
    if (paymentOpts == null) return null;
    
    final stripe = paymentOpts['stripe'] as Map<String, dynamic>?;
    return stripe?['stripe_publishable_key'] as String?;
  }

  /// Check if Razorpay payment is enabled
  bool get isRazorpayEnabled {
    final paymentOpts = paymentOptions;
    if (paymentOpts == null) return false;
    
    final razorpay = paymentOpts['razorpay'] as Map<String, dynamic>?;
    return razorpay?['razorpay_status'] == '1';
  }

  /// Get maximum file size for chat in bytes
  int get maxChatFileSize {
    final chatOpts = chatSettings;
    if (chatOpts == null) return 10000000; // 10MB default
    
    return chatOpts['max_file_size_in_bytes'] as int? ?? 10000000;
  }

  /// Get maximum characters in text message
  int get maxChatTextLength {
    final chatOpts = chatSettings;
    if (chatOpts == null) return 500; // Default
    
    final maxChars = chatOpts['max_characters_in_text_message'];
    if (maxChars is String) {
      return int.tryParse(maxChars) ?? 500;
    }
    return maxChars as int? ?? 500;
  }

  /// Get online exam terms and conditions
  String get onlineExamTerms {
    return getSetting('online_exam_terms_condition') as String? ?? '';
  }

  /// Refresh settings (reload from local service)
  void refreshSettings() {
    loadAppSettings();
  }
}
