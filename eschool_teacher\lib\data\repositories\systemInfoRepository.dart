import 'package:eschool_teacher/data/models/event.dart';
import 'package:eschool_teacher/data/models/eventSchedule.dart';
import 'package:eschool_teacher/data/models/holiday.dart';
import 'package:eschool_teacher/data/models/sessionYear.dart';
import 'package:eschool_teacher/utils/api.dart';
import 'package:flutter/foundation.dart';

class SystemRepository {
  Future<dynamic> fetchSettings({required String type}) async {
    try {
      final result = await Api.get(
        queryParameters: {"type": type},
        url: Api.settings,
        useAuthToken: false,
      );
      return result['data'];
    } catch (e) {
      // Provide fallback data when settings endpoint is not available
      debugPrint('Settings API not available, using fallback data for type: $type');
      return _getFallbackSettingsData(type);
    }
  }

  /// Provides fallback data when settings endpoint is not available
  dynamic _getFallbackSettingsData(String type) {
    switch (type) {
      case 'app_settings':
        return {
          'app_link': 'https://play.google.com/store/apps/details?id=com.example.eschool.teacher',
          'ios_app_link': 'https://apps.apple.com/app/eschool-teacher',
          'app_version': '1.0.0',
          'ios_app_version': '1.0.0',
          'force_app_update': '0',
          'app_maintenance': '0',
          'session_year': {
            'id': 1,
            'name': '2024-25',
            'start_date': '2024-04-01',
            'end_date': '2025-04-01',
            'default': 1,
          },
          'school_name': 'Mahran Education Center',
          'school_tagline': 'Excellence in Education - Teacher Portal',
          'holiday_days': 'friday,saturday',
          'online_exam_terms_condition': '<p>Please follow exam guidelines</p>',
          'online_payment': '1',
          'is_demo': false,
          'compulsory_fee_payment_mode': '0',
          'is_student_can_pay_fees': '1',
          'chat_settings': {
            'max_files_or_images_in_one_message': '10',
            'max_file_size_in_bytes': '10000000',
            'max_characters_in_text_message': '500',
            'automatically_messages_removed_days': '0',
          },
          'current_semester': {
            'id': 1,
            'name': 'Semester 1',
            'start_month': 1,
            'end_month': 6,
            'current': true,
          },
          'payment_options': {},
        };
      case 'privacy_policy':
        return '<h2>Privacy Policy</h2><p>Your privacy is important to us. This policy outlines how we collect, use, and protect your information.</p>';
      case 'about_us':
        return '<h2>About Us</h2><p>We are dedicated to providing quality education through innovative technology solutions.</p>';
      case 'contact_us':
        return '<h2>Contact Us</h2><p>Email: <EMAIL><br>Phone: ******-567-8900<br>Address: Education Center, Technology District</p>';
      case 'terms_and_condition':
        return '<h2>Terms and Conditions</h2><p>By using this application, you agree to our terms of service and conditions of use.</p>';
      default:
        return '<p>Content not available at the moment.</p>';
    }
  }

  Future<List<Holiday>> fetchHolidays() async {
    try {
      final result = await Api.get(url: Api.holidays, useAuthToken: false);
      return ((result['data'] ?? []) as List)
          .map((holiday) => Holiday.fromJson(Map.from(holiday)))
          .toList();
    } catch (e) {
      throw ApiException(e.toString());
    }
  }

  Future<List<Event>> fetchEvents() async {
    try {
      final result = await Api.get(url: Api.events, useAuthToken: true);
      return ((result['data'] ?? []) as List)
          .map((event) => Event.fromJson(Map.from(event)))
          .toList();
    } catch (e) {
      throw ApiException(e.toString());
    }
  }

  Future<List<EventSchedule>> fetchEventDetails(
      {required String eventId,}) async {
    try {
      final result = await Api.get(
          url: Api.eventDetails,
          useAuthToken: true,
          queryParameters: {"event_id": eventId},);
      return ((result['data'] ?? []) as List)
          .map((event) => EventSchedule.fromJson(Map.from(event)))
          .toList();
    } catch (e) {
      throw ApiException(e.toString());
    }
  }

  Future<List<SessionYear>> fetchSessionYears() async {
    try {
      final result = await Api.get(url: Api.sessionYears, useAuthToken: true);
      return ((result['data'] ?? []) as List)
          .map((event) => SessionYear.fromJson(Map.from(event)))
          .toList();
    } catch (e) {
      throw ApiException(e.toString());
    }
  }
}
