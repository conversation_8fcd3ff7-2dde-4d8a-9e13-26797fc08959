import 'package:bloc/bloc.dart';
import 'package:eschool_teacher/data/models/zoom_meeting.dart';
import 'package:eschool_teacher/data/repositories/zoomRepository.dart';
import 'package:equatable/equatable.dart';

// State
abstract class ZoomState extends Equatable {
  const ZoomState();

  @override
  List<Object?> get props => [];
}

class ZoomInitial extends ZoomState {}

// Create meeting states
class ZoomMeetingCreateInProgress extends ZoomState {}

class ZoomMeetingCreateSuccess extends ZoomState {
  final ZoomMeeting meeting;

  const ZoomMeetingCreateSuccess({required this.meeting});

  @override
  List<Object?> get props => [meeting];
}

class ZoomMeetingCreateFailure extends ZoomState {
  final String errorMessage;

  const ZoomMeetingCreateFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

// Update meeting states
class ZoomMeetingUpdateInProgress extends ZoomState {}

class ZoomMeetingUpdateSuc<PERSON> extends ZoomState {
  final ZoomMeeting meeting;

  const ZoomMeetingUpdateSuccess({required this.meeting});

  @override
  List<Object?> get props => [meeting];
}

class ZoomMeetingUpdateFailure extends ZoomState {
  final String errorMessage;

  const ZoomMeetingUpdateFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

// Delete meeting states
class ZoomMeetingDeleteInProgress extends ZoomState {}

class ZoomMeetingDeleteSuccess extends ZoomState {}

class ZoomMeetingDeleteFailure extends ZoomState {
  final String errorMessage;

  const ZoomMeetingDeleteFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

// Fetch meetings states
class ZoomMeetingsFetchInProgress extends ZoomState {}

class ZoomMeetingsFetchSuccess extends ZoomState {
  final List<ZoomMeeting> meetings;

  const ZoomMeetingsFetchSuccess({required this.meetings});

  @override
  List<Object?> get props => [meetings];
}

class ZoomMeetingsFetchFailure extends ZoomState {
  final String errorMessage;

  const ZoomMeetingsFetchFailure({required this.errorMessage});

  @override
  List<Object?> get props => [errorMessage];
}

// Cubit
class ZoomCubit extends Cubit<ZoomState> {
  final ZoomRepository _zoomRepository;

  ZoomCubit(this._zoomRepository) : super(ZoomInitial());

  // Create a new Zoom meeting
  Future<void> createZoomMeeting({
    required String lessonId,
    required DateTime startTime,
    required DateTime endTime,
    required String topic,
  }) async {
    try {
      emit(ZoomMeetingCreateInProgress());
      
      final result = await _zoomRepository.createZoomMeeting(
        lessonId: lessonId,
        startTime: startTime,
        endTime: endTime,
        topic: topic,
      );
      
      if (result['error']) {
        emit(ZoomMeetingCreateFailure(errorMessage: result['message']));
      } else {
        emit(ZoomMeetingCreateSuccess(meeting: result['meeting']));
      }
    } catch (e) {
      emit(ZoomMeetingCreateFailure(errorMessage: e.toString()));
    }
  }

  // Update an existing Zoom meeting
  Future<void> updateZoomMeeting({
    required String meetingId,
    required DateTime startTime,
    required DateTime endTime,
    String? topic,
  }) async {
    try {
      emit(ZoomMeetingUpdateInProgress());
      
      final result = await _zoomRepository.updateZoomMeeting(
        meetingId: meetingId,
        startTime: startTime,
        endTime: endTime,
        topic: topic,
      );
      
      if (result['error']) {
        emit(ZoomMeetingUpdateFailure(errorMessage: result['message']));
      } else {
        emit(ZoomMeetingUpdateSuccess(meeting: result['meeting']));
      }
    } catch (e) {
      emit(ZoomMeetingUpdateFailure(errorMessage: e.toString()));
    }
  }

  // Delete a Zoom meeting
  Future<void> deleteZoomMeeting({
    required String meetingId,
  }) async {
    try {
      emit(ZoomMeetingDeleteInProgress());
      
      final result = await _zoomRepository.deleteZoomMeeting(
        meetingId: meetingId,
      );
      
      if (result['error']) {
        emit(ZoomMeetingDeleteFailure(errorMessage: result['message']));
      } else {
        emit(ZoomMeetingDeleteSuccess());
      }
    } catch (e) {
      emit(ZoomMeetingDeleteFailure(errorMessage: e.toString()));
    }
  }

  // Fetch Zoom meetings
  Future<void> fetchZoomMeetings({
    String? lessonId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      emit(ZoomMeetingsFetchInProgress());
      
      final result = await _zoomRepository.getZoomMeetings(
        lessonId: lessonId,
        startDate: startDate,
        endDate: endDate,
      );
      
      if (result['error']) {
        emit(ZoomMeetingsFetchFailure(errorMessage: result['message']));
      } else {
        emit(ZoomMeetingsFetchSuccess(meetings: result['meetings']));
      }
    } catch (e) {
      emit(ZoomMeetingsFetchFailure(errorMessage: e.toString()));
    }
  }
} 