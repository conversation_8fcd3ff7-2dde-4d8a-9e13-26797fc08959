import 'package:eschool_teacher/cubits/timeTableCubit.dart';
import 'package:eschool_teacher/cubits/updateTimetableLinkCubit.dart';
import 'package:eschool_teacher/data/models/timeTableSlot.dart';
import 'package:eschool_teacher/data/repositories/teacherRepository.dart';
import 'package:eschool_teacher/ui/screens/class/widgets/subjectImageContainer.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/addUpdateTimetableLinkBottomsheetContainer.dart';
import 'package:eschool_teacher/ui/styles/designSystem.dart';
import 'package:eschool_teacher/ui/widgets/customShimmerContainer.dart';
import 'package:eschool_teacher/ui/widgets/errorContainer.dart';
import 'package:eschool_teacher/ui/widgets/noDataContainer.dart';
import 'package:eschool_teacher/ui/widgets/screenTopBackgroundContainer.dart';
import 'package:eschool_teacher/ui/widgets/shimmerLoadingContainer.dart';
import 'package:eschool_teacher/utils/animationConfiguration.dart';
import 'package:eschool_teacher/utils/constants.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

class TimeTableContainer extends StatefulWidget {
  const TimeTableContainer({super.key});

  @override
  State<TimeTableContainer> createState() => _TimeTableContainerState();
}

class _TimeTableContainerState extends State<TimeTableContainer> {
  late int _currentSelectedDayIndex = DateTime.now().weekday - 1;
  DateTime _selectedStartDate = DateTime.now().subtract(const Duration(days: 7));
  DateTime _selectedEndDate = DateTime.now().add(const Duration(days: 7));
  bool _showCalendarView = false;

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      if (context.mounted) {
        context.read<TimeTableCubit>().fetchTimeTable();
      }
    });
  }

  Widget _buildAppBar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingLg,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignSystem.primaryColor,
            DesignSystem.primaryLight,
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  UiUtils.getTranslatedLabel(context, scheduleKey),
                  style: DesignSystem.headingMedium.copyWith(
                    color: DesignSystem.textOnPrimary,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      onPressed: _showDateRangePicker,
                      icon: const Icon(
                        Icons.date_range,
                        color: DesignSystem.textOnPrimary,
                        size: DesignSystem.iconLg,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        setState(() {
                          _showCalendarView = !_showCalendarView;
                        });
                      },
                      icon: Icon(
                        _showCalendarView ? Icons.list : Icons.calendar_month,
                        color: DesignSystem.textOnPrimary,
                        size: DesignSystem.iconLg,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: DesignSystem.spacingMd),
            _buildDateRangeDisplay(),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeDisplay() {
    return Container(
      padding: DesignSystem.paddingMd,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: DesignSystem.borderRadiusLg,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.calendar_today,
            color: DesignSystem.textOnPrimary,
            size: DesignSystem.iconSm,
          ),
          SizedBox(width: DesignSystem.spacingSm),
          Text(
            "${UiUtils.formatDate(_selectedStartDate)} - ${UiUtils.formatDate(_selectedEndDate)}",
            style: DesignSystem.bodyMedium.copyWith(
              color: DesignSystem.textOnPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showDateRangePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: DateTimeRange(
        start: _selectedStartDate,
        end: _selectedEndDate,
      ),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: DesignSystem.primaryColor,
              onPrimary: DesignSystem.textOnPrimary,
              surface: DesignSystem.surfaceColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedStartDate = picked.start;
        _selectedEndDate = picked.end;
      });
      // Refresh timetable data for the new date range
      if (context.mounted) {
        context.read<TimeTableCubit>().fetchTimeTable();
      }
    }
  }

  Widget _buildDayContainer(int index) {
    final isSelected = index == _currentSelectedDayIndex;
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentSelectedDayIndex = index;
        });
      },
      child: AnimatedContainer(
        duration: DesignSystem.animationFast,
        margin: EdgeInsets.symmetric(horizontal: DesignSystem.spacingXs),
        padding: EdgeInsets.symmetric(
          horizontal: DesignSystem.spacingMd,
          vertical: DesignSystem.spacingSm,
        ),
        decoration: BoxDecoration(
          color: isSelected ? DesignSystem.primaryColor : DesignSystem.surfaceColor,
          borderRadius: DesignSystem.borderRadiusLg,
          border: Border.all(
            color: isSelected ? DesignSystem.primaryColor : DesignSystem.textTertiary,
            width: 1,
          ),
          boxShadow: isSelected ? DesignSystem.shadowSm : null,
        ),
        child: Text(
          UiUtils.weekDays[index],
          style: DesignSystem.labelMedium.copyWith(
            color: isSelected ? DesignSystem.textOnPrimary : DesignSystem.textPrimary,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildDays() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: DesignSystem.spacing2xl),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: List.generate(
            UiUtils.weekDays.length,
            (index) => _buildDayContainer(index),
          ),
        ),
      ),
    );
  }

  Future<void> _openAddUpdateTimetableBottomsheetContainer(
    TimeTableSlot timeTableSlot,
  ) async {
    final didChange = await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: DesignSystem.surfaceColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(DesignSystem.radius2xl),
              topRight: Radius.circular(DesignSystem.radius2xl),
            ),
          ),
          child: BlocProvider(
            create: (context) => UpdateTimetableLinkCubit(TeacherRepository()),
            child: AddUpdateTimetableLinkBottomsheetContaienr(
              timetableSlot: timeTableSlot,
            ),
          ),
        );
      },
    );
    if (didChange == true) {
      setState(() {});
    }
  }

  Widget _buildTimeTableSlotDetailsContainer(TimeTableSlot timeTableSlot) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingSm,
      ),
      decoration: DesignSystem.cardDecoration,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openAddUpdateTimetableBottomsheetContainer(timeTableSlot),
          borderRadius: DesignSystem.borderRadiusLg,
          child: Padding(
            padding: DesignSystem.paddingLg,
            child: Row(
              children: [
                SubjectImageContainer(
                  showShadow: false,
                  height: 60,
                  width: 60,
                  radius: DesignSystem.radiusLg,
                  subject: timeTableSlot.subject,
                ),
                SizedBox(width: DesignSystem.spacingLg),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: DesignSystem.spacingSm,
                              vertical: DesignSystem.spacing2xs,
                            ),
                            decoration: BoxDecoration(
                              color: DesignSystem.primaryColor.withValues(alpha: 0.1),
                              borderRadius: DesignSystem.borderRadiusSm,
                            ),
                            child: Text(
                              "${UiUtils.formatTime(timeTableSlot.startTime)} - ${UiUtils.formatTime(timeTableSlot.endTime)}",
                              style: DesignSystem.labelSmall.copyWith(
                                color: DesignSystem.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          SizedBox(width: DesignSystem.spacingSm),
                          Expanded(
                            child: Text(
                              timeTableSlot.classSectionDetails.getClassSectionNameWithMedium(),
                              style: DesignSystem.bodySmall.copyWith(
                                color: DesignSystem.textSecondary,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: DesignSystem.spacingSm),
                      Text(
                        timeTableSlot.subject.showType
                            ? timeTableSlot.subject.subjectNameWithType
                            : timeTableSlot.subject.name,
                        style: DesignSystem.titleMedium,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (timeTableSlot.hasCustomLink) ...[
                        SizedBox(height: DesignSystem.spacingSm),
                        Row(
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: () {
                                  try {
                                    launchUrl(
                                      Uri.parse(timeTableSlot.linkCustomUrl!),
                                      mode: LaunchMode.externalApplication,
                                    );
                                  } catch (_) {}
                                },
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: DesignSystem.spacingSm,
                                    vertical: DesignSystem.spacing2xs,
                                  ),
                                  decoration: BoxDecoration(
                                    color: DesignSystem.successColor.withValues(alpha: 0.1),
                                    borderRadius: DesignSystem.borderRadiusSm,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.link,
                                        size: DesignSystem.iconSm,
                                        color: DesignSystem.successColor,
                                      ),
                                      SizedBox(width: DesignSystem.spacing2xs),
                                      Flexible(
                                        child: Text(
                                          timeTableSlot.linkName!,
                                          style: DesignSystem.labelSmall.copyWith(
                                            color: DesignSystem.successColor,
                                            fontWeight: FontWeight.w600,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: DesignSystem.spacingSm),
                            Container(
                              padding: EdgeInsets.all(DesignSystem.spacingXs),
                              decoration: BoxDecoration(
                                color: DesignSystem.primaryColor.withValues(alpha: 0.1),
                                borderRadius: DesignSystem.borderRadiusSm,
                              ),
                              child: const Icon(
                                Icons.edit_outlined,
                                size: DesignSystem.iconSm,
                                color: DesignSystem.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ] else ...[
                        SizedBox(height: DesignSystem.spacingSm),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: DesignSystem.spacingSm,
                            vertical: DesignSystem.spacing2xs,
                          ),
                          decoration: BoxDecoration(
                            color: DesignSystem.warningColor.withValues(alpha: 0.1),
                            borderRadius: DesignSystem.borderRadiusSm,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.add_link,
                                size: DesignSystem.iconSm,
                                color: DesignSystem.warningColor,
                              ),
                              SizedBox(width: DesignSystem.spacing2xs),
                              Text(
                                "Add Zoom Link",
                                style: DesignSystem.labelSmall.copyWith(
                                  color: DesignSystem.warningColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<TimeTableSlot> _buildTimeTableSlots(List<TimeTableSlot> timeTableSlot) {
    final dayWiseTimeTableSlots = timeTableSlot
        .where((element) => element.day == _currentSelectedDayIndex + 1)
        .toList();
    return dayWiseTimeTableSlots;
  }

  Widget _buildTimeTableShimmerLoadingContainer() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingSm,
      ),
      padding: DesignSystem.paddingLg,
      decoration: DesignSystem.cardDecoration,
      child: ShimmerLoadingContainer(
        child: Row(
          children: [
            CustomShimmerContainer(
              height: 60,
              width: 60,
              borderRadius: DesignSystem.radiusLg,
            ),
            SizedBox(width: DesignSystem.spacingLg),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomShimmerContainer(
                    height: 16,
                    width: double.infinity,
                    borderRadius: DesignSystem.radiusSm,
                  ),
                  SizedBox(height: DesignSystem.spacingSm),
                  CustomShimmerContainer(
                    height: 14,
                    width: 150,
                    borderRadius: DesignSystem.radiusSm,
                  ),
                  SizedBox(height: DesignSystem.spacingSm),
                  CustomShimmerContainer(
                    height: 12,
                    width: 100,
                    borderRadius: DesignSystem.radiusSm,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarView() {
    return BlocBuilder<TimeTableCubit, TimeTableState>(
      builder: (context, state) {
        if (state is TimeTableFetchSuccess) {
          return Container(
            margin: EdgeInsets.all(DesignSystem.spacing2xl),
            decoration: DesignSystem.cardDecoration,
            child: CalendarDatePicker(
              initialDate: DateTime.now(),
              firstDate: _selectedStartDate,
              lastDate: _selectedEndDate,
              onDateChanged: (date) {
                setState(() {
                  _currentSelectedDayIndex = date.weekday - 1;
                });
              },
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildTimeTable() {
    return BlocBuilder<TimeTableCubit, TimeTableState>(
      builder: (context, state) {
        if (state is TimeTableFetchSuccess) {
          final timetableSlots = _buildTimeTableSlots(state.timetableSlots);
          return timetableSlots.isEmpty
              ? Container(
                  margin: EdgeInsets.all(DesignSystem.spacing2xl),
                  child: NoDataContainer(
                    key: isApplicationItemAnimationOn ? UniqueKey() : null,
                    titleKey: noLecturesKey,
                  ),
                )
              : Column(
                  children: List.generate(
                    timetableSlots.length,
                    (index) => Animate(
                      key: isApplicationItemAnimationOn
                          ? ValueKey(
                              timetableSlots[index].id +
                                  _currentSelectedDayIndex.toString(),
                            )
                          : null,
                      effects: listItemAppearanceEffects(
                        itemIndex: index,
                        totalLoadedItems: timetableSlots.length,
                      ),
                      child: _buildTimeTableSlotDetailsContainer(
                        timetableSlots[index],
                      ),
                    ),
                  ),
                );
        }

        if (state is TimeTableFetchFailure) {
          return Container(
            margin: EdgeInsets.all(DesignSystem.spacing2xl),
            child: ErrorContainer(
              key: isApplicationItemAnimationOn ? UniqueKey() : null,
              errorMessageCode: state.errorMessage,
              onTapRetry: () {
                context.read<TimeTableCubit>().fetchTimeTable();
              },
            ),
          );
        }

        return Column(
          children: List.generate(
            UiUtils.defaultShimmerLoadingContentCount,
            (index) => _buildTimeTableShimmerLoadingContainer(),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.backgroundColor,
      body: Column(
        children: [
          _buildAppBar(),
          if (!_showCalendarView) ...[
            SizedBox(height: DesignSystem.spacingLg),
            _buildDays(),
          ],
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.only(
                bottom: DesignSystem.spacing2xl,
                top: DesignSystem.spacingLg,
              ),
              child: _showCalendarView ? _buildCalendarView() : _buildTimeTable(),
            ),
          ),
        ],
      ),
    );
  }
}
