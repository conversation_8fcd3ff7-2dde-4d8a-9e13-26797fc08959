# eSchool Shared Design System

A comprehensive Flutter design system package shared between the eSchool student and teacher applications. This design system ensures visual consistency, reduces code duplication, and provides a unified user experience across both apps.

## 🎨 Features

- **Design Tokens**: Centralized colors, typography, spacing, and shadows
- **Unified Theme**: Complete Material Design 3 theme configuration
- **Shared Widgets**: Reusable UI components following the design system
- **App-Specific Customization**: Support for student and teacher app variations
- **Accessibility**: Built-in accessibility features and semantic labels
- **Responsive Design**: Mobile-first approach with responsive utilities

## 📦 Installation

Add this package to your `pubspec.yaml`:

```yaml
dependencies:
  shared_design_system:
    path: ../shared_design_system
```

Then run:

```bash
flutter pub get
```

## 🚀 Usage

### Basic Setup

Import the design system in your app's `main.dart`:

```dart
import 'package:shared_design_system/shared_design_system.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'eSchool App',
      theme: AppTheme.light, // Apply the design system theme
      home: HomeScreen(),
    );
  }
}
```

### Using Design Tokens

#### Colors
```dart
import 'package:shared_design_system/shared_design_system.dart';

Container(
  color: AppColors.primary,
  child: Text(
    'Hello World',
    style: TextStyle(color: AppColors.onPrimary),
  ),
)
```

#### Typography
```dart
Text(
  'Heading',
  style: AppTypography.headlineLarge,
)

Text(
  'Body text',
  style: AppTypography.bodyMedium,
)
```

#### Spacing
```dart
Padding(
  padding: EdgeInsets.all(AppSpacing.medium),
  child: Column(
    children: [
      Text('Item 1'),
      UiUtils.mediumVerticalSpace,
      Text('Item 2'),
    ],
  ),
)
```

### Using Shared Widgets

#### Buttons
```dart
// Primary button
AppButton.primary(
  text: 'Save',
  onPressed: () => print('Saved!'),
  icon: Icon(Icons.save),
)

// Custom button
CustomRoundedButton(
  buttonName: 'Custom Action',
  onPressed: () {},
  backgroundColor: AppColors.success,
  isLoading: false,
)
```

#### Form Fields
```dart
// Standard text field
AppFormField.text(
  hintText: 'Enter your name',
  controller: _nameController,
  labelText: 'Full Name',
)

// Email field
AppFormField.email(
  hintText: 'Enter email',
  controller: _emailController,
)

// Password field
AppFormField.password(
  hintText: 'Enter password',
  controller: _passwordController,
  isPasswordVisible: _isPasswordVisible,
  onVisibilityToggle: () {
    setState(() => _isPasswordVisible = !_isPasswordVisible);
  },
)
```

#### App Bar
```dart
// Standard app bar
AppBarVariants.standard(
  title: 'Home',
  actions: [
    IconButton(
      icon: Icon(Icons.search),
      onPressed: () {},
    ),
  ],
)

// Search app bar
AppBarVariants.search(
  searchController: _searchController,
  hintText: 'Search...',
  onSearchChanged: (value) => print('Searching: $value'),
)
```

#### Bottom Navigation
```dart
AppBottomNavigationBar(
  currentIndex: _currentIndex,
  onTap: (index) => setState(() => _currentIndex = index),
  items: [
    AppNavigationItems.home(),
    AppNavigationItems.assignments(badge: '3'),
    AppNavigationItems.profile(),
  ],
)
```

#### Progress Indicators
```dart
// Simple loading indicator
AppProgressIndicator.medium()

// Progress with percentage
AppProgressIndicator.withPercentage(
  value: 0.75, // 75%
  color: AppColors.success,
)

// Loading overlay
AppProgressIndicator.overlay(
  isLoading: _isLoading,
  loadingText: 'Please wait...',
  child: YourContentWidget(),
)
```

## 🎯 App-Specific Customization

### Student App Example
```dart
// Use student-specific navigation items
AppBottomNavigationBar(
  items: [
    AppNavigationItems.home(),
    AppNavigationItems.assignments(),
    AppNavigationItems.exam(),
    AppNavigationItems.results(),
    AppNavigationItems.profile(),
  ],
  // ... other properties
)

// Use student accent color for special elements
Container(
  color: AppColors.studentAccent,
  // ...
)
```

### Teacher App Example
```dart
// Use teacher-specific navigation items
AppBottomNavigationBar(
  items: [
    AppNavigationItems.teacherHome(),
    AppNavigationItems.classes(),
    AppNavigationItems.lessons(),
    AppNavigationItems.earnings(),
    AppNavigationItems.teacherProfile(),
  ],
  // ... other properties
)

// Use teacher accent color
Container(
  color: AppColors.teacherAccent,
  // ...
)
```

## 🛠️ Utilities

### Responsive Design
```dart
// Check screen size
if (UiUtils.isMobile(context)) {
  // Mobile layout
} else if (UiUtils.isTablet(context)) {
  // Tablet layout
} else {
  // Desktop layout
}

// Responsive padding
Container(
  padding: UiUtils.getResponsivePadding(context),
  child: YourWidget(),
)

// Responsive grid columns
GridView.builder(
  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
    crossAxisCount: UiUtils.getResponsiveGridColumns(context),
  ),
  // ...
)
```

### UI Helpers
```dart
// Show consistent snackbar
UiUtils.showSnackBar(
  context,
  'Operation completed successfully!',
  action: SnackBarAction(
    label: 'Undo',
    onPressed: () {},
  ),
)

// Dismiss keyboard
UiUtils.dismissKeyboard(context);

// Focus management
UiUtils.focusNext(context);
```

## 🎨 Design Tokens Reference

### Colors
- **Primary**: `AppColors.primary` (#5F97B7)
- **Highlight**: `AppColors.highlight` (#25536D)
- **Background**: `AppColors.background` (#E9E9E9)
- **Surface**: `AppColors.surface` (#FFFFFF)
- **Success**: `AppColors.success` (#4CAF50)
- **Warning**: `AppColors.warning` (#FF9800)
- **Error**: `AppColors.error` (#E53935)

### Typography Scale
- **Display**: Large headings (32px, 28px, 24px)
- **Headline**: Section headings (22px, 20px, 18px)
- **Title**: Component titles (16px, 14px, 12px)
- **Body**: Content text (16px, 14px, 12px)
- **Label**: UI labels (14px, 12px, 10px)

### Spacing Scale
- **XS**: 4px (`AppSpacing.xs`)
- **SM**: 8px (`AppSpacing.sm`)
- **MD**: 12px (`AppSpacing.md`)
- **LG**: 16px (`AppSpacing.lg`)
- **XL**: 20px (`AppSpacing.xl`)
- **XXL**: 24px (`AppSpacing.xxl`)

### Shadows & Elevation
- **Level 1**: Subtle cards (`AppShadows.card`)
- **Level 2**: Raised elements (`AppShadows.button`)
- **Level 3**: Floating elements (`AppShadows.floatingActionButton`)
- **Level 4**: Modals (`AppShadows.dialog`)

## 📱 Widget Categories

### Buttons
- `CustomRoundedButton`: Highly customizable base button
- `AppButton.*`: Pre-configured button variants (primary, secondary, outlined, text, success, warning, error)

### Forms
- `BottomSheetTextField`: Flexible text input field
- `AppFormField.*`: Pre-configured form fields (text, email, password, phone, number, textArea, search)

### Layout
- `CustomAppBar`: Customizable app bar with variants
- `AppBarVariants.*`: Pre-configured app bar types (standard, search, profile, transparent, gradient)

### Navigation
- `AppBottomNavigationBar`: Shared bottom navigation
- `AppNavigationItems.*`: Pre-built navigation items for student and teacher apps
- `NavigationBadge`: Badge component for navigation items

### Feedback
- `CustomCircularProgressIndicator`: Progress indicators with customization
- `AppProgressIndicator.*`: Pre-configured progress variants

## 🔧 Development

### Project Structure
```
shared_design_system/
├── lib/
│   ├── shared_design_system.dart          # Main export file
│   └── src/
│       ├── tokens/                        # Design tokens
│       │   ├── colors.dart
│       │   ├── typography.dart
│       │   ├── spacing.dart
│       │   └── shadows.dart
│       ├── theme/                         # Theme configuration
│       │   └── app_theme.dart
│       ├── widgets/                       # Shared widgets
│       │   ├── buttons/
│       │   ├── forms/
│       │   ├── layout/
│       │   ├── navigation/
│       │   └── feedback/
│       └── utils/                         # Utility functions
│           └── ui_utils.dart
├── pubspec.yaml
└── README.md
```

### Adding New Components

1. Create the widget file in the appropriate category folder
2. Follow the existing naming conventions (`Custom*` for base widgets, `App*` for variants)
3. Use design tokens for colors, typography, and spacing
4. Include accessibility features
5. Export the new widget in `shared_design_system.dart`

### Design Principles

- **Consistency**: All components follow the same design language
- **Flexibility**: Components are customizable while maintaining consistency
- **Accessibility**: Built-in support for screen readers and accessibility features
- **Performance**: Optimized widgets with minimal rebuilds
- **Documentation**: Comprehensive documentation and examples

## 🤝 Contributing

When contributing to the design system:

1. Follow the existing code style and patterns
2. Add comprehensive documentation for new components
3. Include usage examples
4. Test components in both student and teacher apps
5. Update this README with new features

## 📄 License

This design system is part of the eSchool project and follows the same licensing terms. 