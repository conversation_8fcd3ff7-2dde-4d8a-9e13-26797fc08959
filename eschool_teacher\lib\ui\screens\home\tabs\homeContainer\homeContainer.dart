import 'package:eschool_teacher/app/routes.dart';
import 'package:eschool_teacher/cubits/dashboardCubit.dart';

import 'package:eschool_teacher/cubits/timeTableCubit.dart';
import 'package:eschool_teacher/cubits/updateStudentLeaveStatusCubit.dart';
import 'package:eschool_teacher/data/models/classSectionDetails.dart';
import 'package:eschool_teacher/data/models/event.dart';
import 'package:eschool_teacher/data/models/exam.dart';
import 'package:eschool_teacher/data/models/studentLeave.dart';

import 'package:eschool_teacher/data/repositories/studentLeaveRepository.dart';
import 'package:eschool_teacher/ui/screens/academicCalendar/widgets/listItemForEvents.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/homeContainer/widgets/classTeacherClassContainer.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/homeContainer/widgets/homeContainerAppBarContainer.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/homeContainer/widgets/homeContainerExamItemContainer.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/homeContainer/widgets/homeContainerExploreAcademicsItemContainer.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/homeContainer/widgets/homeContainerShimmerContainer.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/homeContainer/widgets/homeContainerStaffLeavesContainer.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/homeContainer/widgets/homeContainerStudentLeaveRequestContainer.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/homeContainer/widgets/homeContainerTodaysTimetableContainer.dart';
import 'package:eschool_teacher/ui/screens/home/<USER>/homeContainer/widgets/subjectTeacherClassContainer.dart';
import 'package:eschool_teacher/ui/styles/colors.dart';
import 'package:eschool_teacher/ui/widgets/errorContainer.dart';

import 'package:eschool_teacher/ui/widgets/noDataContainer.dart';
import 'package:eschool_teacher/utils/animationConfiguration.dart';
import 'package:eschool_teacher/utils/fixHeightDeligate.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HomeContainer extends StatefulWidget {
  const HomeContainer({super.key});

  @override
  State<HomeContainer> createState() => _HomeContainerState();
}

class _HomeContainerState extends State<HomeContainer> {
  late List<MenuContainerDetails> quickActionsItems = [
    MenuContainerDetails(
      route: Routes.assignments,
      iconPath: UiUtils.getImagePath("assignment_icon.svg"),
      title: UiUtils.getTranslatedLabel(context, assignmentsKey),
    ),
    MenuContainerDetails(
      route: Routes.announcements,
      iconPath: UiUtils.getImagePath("announcment_icon.svg"),
      title: UiUtils.getTranslatedLabel(context, announcementsKey),
    ),
    MenuContainerDetails(
      route: Routes.lessons,
      iconPath: UiUtils.getImagePath("lesson.svg"),
      title: UiUtils.getTranslatedLabel(context, chaptersKey),
    ),
    MenuContainerDetails(
      route: Routes.earnings,
      iconPath: UiUtils.getImagePath("manage_leaves.svg"),
      title: "Earnings",
    ),
  ];

  late List<MenuContainerDetails> exploreAcademicsItems = [
    MenuContainerDetails(
      route: Routes.topics,
      iconPath: UiUtils.getImagePath("topics.svg"),
      title: UiUtils.getTranslatedLabel(context, topicsKey),
    ),
    MenuContainerDetails(
      route: Routes.academicCalendar,
      iconPath: UiUtils.getImagePath("holiday_icon.svg"),
      title: UiUtils.getTranslatedLabel(context, academicCalendarKey),
    ),
    MenuContainerDetails(
      route: Routes.exams,
      iconPath: UiUtils.getImagePath("exam_icon.svg"),
      title: UiUtils.getTranslatedLabel(context, examsKey),
    ),
    if (context.read<DashboardCubit>().primaryClass() != null)
      MenuContainerDetails(
        route: Routes.addResultForAllStudents,
        iconPath: UiUtils.getImagePath("result_icon.svg"),
        title: UiUtils.getTranslatedLabel(context, addResultKey),
      ),
    MenuContainerDetails(
      route: Routes.manageLeaves,
      iconPath: UiUtils.getImagePath("manage_leaves.svg"),
      title: UiUtils.getTranslatedLabel(context, manageLeavesKey),
    ),
    MenuContainerDetails(
      route: Routes.manageStudentLeaves,
      iconPath: UiUtils.getImagePath("manage_student_leaves.svg"),
      title: UiUtils.getTranslatedLabel(context, manageStudentLeavesKey),
    ),
  ];

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      if (mounted) {
        context.read<DashboardCubit>().fetchDashboard();
      }
    });
  }

  Widget _buildTitleText(
    String textKey, {
    bool removePadding = false,
    void Function()? viewAllTap,
  }) {
    return Padding(
      padding: removePadding
          ? EdgeInsets.zero
          : EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * (0.075),
            ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              UiUtils.getTranslatedLabel(context, textKey),
              style: const TextStyle(
                color: onSurfaceColor,
                fontSize: 16.0,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          if (viewAllTap != null) ...[
            const SizedBox(
              width: 8,
            ),
            GestureDetector(
              onTap: viewAllTap,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    UiUtils.getTranslatedLabel(context, viewAllKey),
                    style: const TextStyle(
                      color: onSurfaceColor,
                      fontSize: 14.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(
                    width: 4,
                  ),
                  const Icon(
                    Icons.arrow_right_alt,
                    color: onSurfaceColor,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAnnouncementsBanner() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MediaQuery.of(context).size.width * (0.075),
      ),
      height: 160,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          colors: [
            primaryColor.withValues(alpha: 0.8),
            primaryColor,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background decoration
          Positioned(
            right: -20,
            top: -20,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  UiUtils.getTranslatedLabel(context, importantAnnouncementKey),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: Text(
                    "خبرا الإعلان بخصوص المصروف وحول الجدول ومن الفين",
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                // Pagination dots
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(4, (index) => Container(
                    margin: const EdgeInsets.symmetric(horizontal: 3),
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: index == 0 
                        ? Colors.white 
                        : Colors.white.withValues(alpha: 0.4),
                      shape: BoxShape.circle,
                    ),
                  )),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssignmentsSection() {
    return SizedBox(
      height: 140,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * (0.075),
        ),
        itemBuilder: (context, index) {
          if (index == 0) {
            return _buildAddAssignmentCard();
          }
          return _buildAssignmentCard(index);
        },
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemCount: 4, // Add assignment button + 3 assignments
      ),
    );
  }

  Widget _buildAddAssignmentCard() {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed(Routes.addAssignment);
      },
      child: Container(
        width: 120,
        decoration: BoxDecoration(
          color: primaryColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: primaryColor.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.add,
                color: Colors.white,
                size: 28,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                UiUtils.getTranslatedLabel(context, addAssignmentKey),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignmentCard(int index) {
    final assignments = [
      {"title": "الوحدة الأولى", "subject": "اللغة العربية", "dueDate": "24/12"},
      {"title": "Math Assignment", "subject": "Mathematics", "dueDate": "25/12"},
      {"title": "Science Project", "subject": "Science", "dueDate": "26/12"},
    ];
    
    final assignment = assignments[index - 1];
    
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed(Routes.assignments);
      },
      child: Container(
        width: 140,
        decoration: BoxDecoration(
          color: surfaceColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Assignment icon section
            Container(
              height: 60,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                color: primaryColor.withValues(alpha: 0.1),
              ),
              child: Center(
                child: Icon(
                  Icons.assignment,
                  size: 32,
                  color: primaryColor,
                ),
              ),
            ),
            // Assignment details
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      assignment["title"]!,
                      style: const TextStyle(
                        color: onSurfaceColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      assignment["subject"]!,
                      style: TextStyle(
                        color: onSurfaceColor.withValues(alpha: 0.7),
                        fontSize: 10,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          size: 12,
                          color: primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          assignment["dueDate"]!,
                          style: TextStyle(
                            color: primaryColor,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReservedLessonsSection() {
    final lessons = [
      {
        "date": "24/12",
        "time": "من 11:30 - 12:30م",
        "subject": "اللغة العربية - صف 11",
      },
      {
        "date": "الأحد 24/12",
        "time": "من 11:30 - 12:30م", 
        "subject": "اللغة العربية - صف 11",
      },
    ];

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MediaQuery.of(context).size.width * (0.075),
      ),
      child: Column(
        children: lessons.map((lesson) => Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: surfaceColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Lesson icon
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.book,
                  color: primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              // Lesson details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${lesson["date"]} ${lesson["time"]}",
                      style: const TextStyle(
                        color: onSurfaceColor,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      lesson["subject"]!,
                      style: TextStyle(
                        color: onSurfaceColor.withValues(alpha: 0.7),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              // Action buttons
              Column(
                children: [
                  SizedBox(
                    width: 60,
                    height: 28,
                    child: ElevatedButton(
                      onPressed: () {
                        // TODO: Implement modify lesson
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade200,
                        foregroundColor: onSurfaceColor,
                        padding: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: Text(
                        UiUtils.getTranslatedLabel(context, modifyKey),
                        style: const TextStyle(fontSize: 10),
                      ),
                    ),
                  ),
                  const SizedBox(height: 6),
                  SizedBox(
                    width: 60,
                    height: 28,
                    child: ElevatedButton(
                      onPressed: () {
                        // TODO: Implement join lesson
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: Text(
                        UiUtils.getTranslatedLabel(context, joinNowKey),
                        style: const TextStyle(fontSize: 10),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        )).toList(),
      ),
    );
  }





  Widget _buildClassTeacherClasses({
    required List<ClassSectionDetails> classes,
  }) {
    return GridView.count(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.symmetric(
        horizontal: MediaQuery.of(context).size.width * (0.075),
      ),
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      crossAxisCount: 2,
      childAspectRatio: 1.3,
      children: List.generate(classes.length, (index) {
        return ClassTeacherClassContainer(
          classDetails: classes[index],
          index: index, //for color picking using index
        );
      }),
    );
  }

  Widget _buildSubjectTeacherClasses({
    required List<ClassSectionDetails> classes,
  }) {
    return SizedBox(
      height: 84,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * (0.075),
        ),
        itemBuilder: (context, index) {
          return SubjectTeacherClassContainer(
            classDetails: classes[index],
            index: index, //for color picking using index
          );
        },
        separatorBuilder: (context, index) {
          return const SizedBox(
            width: 12,
          );
        },
        itemCount: classes.length,
      ),
    );
  }

  Widget _buildExploreAcademics() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: exploreAcademicsItems.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCountAndFixedHeight(
        crossAxisCount: 3,
        height: MediaQuery.of(context).size.width / 3 < 150
            ? 120 //fixed height for smaller devices
            : (MediaQuery.of(context).size.width / 3) - 16,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
      ),
      padding: EdgeInsets.zero,
      itemBuilder: (context, index) {
        return HomeContainerExploreAcademicsItemContainer(
          item: exploreAcademicsItems[index],
        );
      },
    );
  }

  Widget _buildEventList(List<Event> events) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: MediaQuery.of(context).size.width * (0.075),
      ),
      child: Column(
        children: List.generate(
          events.length,
          (index) => Animate(
            effects: listItemAppearanceEffects(
              itemIndex: index,
              totalLoadedItems: events.length,
            ),
            child: EventItemContainer(
              event: events[index],
              backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUpcomingExams(List<Exam> exams) {
    return SizedBox(
      height: 130,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * (0.075),
        ),
        itemBuilder: (context, index) {
          return HomeContainerExamItemContainer(exam: exams[index]);
        },
        separatorBuilder: (context, index) => const SizedBox(
          width: 8,
        ),
        itemCount: exams.length,
      ),
    );
  }

  Widget _buildStudentPendingLeaveRequests({
    required List<StudentLeave> leaveRequests,
  }) {
    return SizedBox(
      height: 130,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width * (0.075),
        ),
        itemBuilder: (context, index) {
          return BlocProvider(
            create: (context) =>
                UpdateStudentLeaveStatusCubit(StudentLeaveRepository()),
            child: HomeContainerStudentLeaveRequestContainer(
              studentLeave: leaveRequests[index],
            ),
          );
        },
        separatorBuilder: (context, index) => const SizedBox(
          width: 8,
        ),
        itemCount: leaveRequests.length,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bodyContentPadding = EdgeInsets.only(
      bottom: UiUtils.getScrollViewBottomPadding(context),
      top: UiUtils.getScrollViewTopPadding(
        context: context,
        appBarHeightPercentage: UiUtils.appBarMediumHeightPercentage,
      ),
    );
    return Container(
      color: Theme.of(context).colorScheme.surface,
      height: double.maxFinite,
      width: double.maxFinite,
      child: Stack(
        children: [
          Align(
            alignment: AlignmentDirectional.topStart,
            child: BlocBuilder<DashboardCubit, DashboardState>(
              builder: (context, state) {
                if (state is DashboardFetchSuccess) {
                  if ((state.primaryClass?.isEmpty ?? true) &&
                      state.classes.isEmpty) {
                    return Padding(
                      padding: bodyContentPadding,
                      child: Center(
                        child: NoDataContainer(
                          titleKey: noClassAssignedKey,
                          showRetryButton: true,
                          onTapRetry: () {
                            context.read<DashboardCubit>().fetchDashboard();
                          },
                        ),
                      ),
                    );
                  }
                  return RefreshIndicator(
                    displacement: UiUtils.getScrollViewTopPadding(
                      context: context,
                      appBarHeightPercentage:
                          UiUtils.appBarMediumHeightPercentage,
                    ),
                    color: Theme.of(context).colorScheme.primary,
                    onRefresh: () {
                      return context.read<DashboardCubit>().fetchDashboard();
                    },
                    child: SizedBox(
                      height: double.maxFinite,
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: bodyContentPadding,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Announcements Banner
                            _buildAnnouncementsBanner(),
                            const SizedBox(height: 24),

                            // Assignments Section
                            _buildTitleText(assignmentsKey),
                            const SizedBox(height: 12),
                            _buildAssignmentsSection(),
                            const SizedBox(height: 24),

                            // Reserved Lessons Section
                            _buildTitleText(reservedLessonsKey),
                            const SizedBox(height: 12),
                            _buildReservedLessonsSection(),
                            const SizedBox(height: 24),

                            ///[Class Teacher]
                            if (state.primaryClass?.isNotEmpty ?? false) ...[
                              _buildTitleText(classTeacherKey),
                              const SizedBox(
                                height: 12,
                              ),
                              _buildClassTeacherClasses(
                                classes: state.primaryClass ?? [],
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                            ],

                            ///[Subject Teacher]
                            if (state.classes.isNotEmpty) ...[
                              const SizedBox(
                                height: 12,
                              ),
                              _buildTitleText(myClassesKey),
                              const SizedBox(
                                height: 12,
                              ),
                              _buildSubjectTeacherClasses(
                                classes: state.classes,
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                            ],

                            ///[Student Pending Leave Requests]
                            if (state.pendingLeaveRequests.isNotEmpty) ...[
                              const SizedBox(
                                height: 12,
                              ),
                              _buildTitleText(
                                studentPendingLeaveRequestsKey,
                                viewAllTap: () {
                                  Navigator.of(context)
                                      .pushNamed(Routes.manageStudentLeaves);
                                },
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              _buildStudentPendingLeaveRequests(
                                leaveRequests: state.pendingLeaveRequests,
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                            ],

                            ///[Explore Academics]
                            const SizedBox(
                              height: 6,
                            ),
                            Container(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              padding: EdgeInsets.symmetric(
                                horizontal:
                                    MediaQuery.of(context).size.width * (0.075),
                                vertical: 12,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  _buildTitleText(
                                    exploreAcademicsKey,
                                    removePadding: true,
                                  ),
                                  const SizedBox(
                                    height: 12,
                                  ),
                                  _buildExploreAcademics(),
                                ],
                              ),
                            ),
                            const SizedBox(
                              height: 12,
                            ),

                            ///[Todays Timetable]
                            if (state.todaysTimetable.isNotEmpty) ...[
                              _buildTitleText(todaysTimetableKey),
                              const SizedBox(
                                height: 12,
                              ),
                              if (context.watch<TimeTableCubit>().state
                                  is TimeTableFetchSuccess)
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal:
                                        MediaQuery.of(context).size.width *
                                            (0.075),
                                  ),
                                  child: HomeContainerTodaysTimetableContainer(
                                    timeTableSlots: state.todaysTimetable,
                                  ),
                                ),
                              const SizedBox(
                                height: 12,
                              ),
                            ],

                            ///[Upcoming Exams]
                            if (state.upcomingExams.isNotEmpty) ...[
                              Container(
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                                child: Column(
                                  children: [
                                    const SizedBox(
                                      height: 16,
                                    ),
                                    _buildTitleText(
                                      upcomingExamsKey,
                                      viewAllTap: () {
                                        Navigator.of(context)
                                            .pushNamed(Routes.exams);
                                      },
                                    ),
                                    const SizedBox(
                                      height: 12,
                                    ),
                                    _buildUpcomingExams(
                                      state.upcomingExams,
                                    ),
                                    const SizedBox(
                                      height: 16,
                                    ),
                                  ],
                                ),
                              ),
                            ],

                            ///[Staff Leaves]
                            if (state.hasStaffLeaves) ...[
                              const SizedBox(
                                height: 18,
                              ),
                              _buildTitleText(
                                staffLeavesKey,
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal:
                                      MediaQuery.of(context).size.width *
                                          (0.075),
                                ),
                                child: HomeContainerStaffLeavesContainer(
                                  today: state.todayStaffLeave,
                                  tomorrow: state.tomorrowStaffLeave,
                                  upcoming: state.upcomingStaffLeave,
                                ),
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                            ],

                            ///[Upcoming Events]
                            if (state.upcomingEvents.isNotEmpty) ...[
                              const SizedBox(
                                height: 12,
                              ),
                              _buildTitleText(
                                upcomingEventsKey,
                                viewAllTap: () {
                                  Navigator.of(context)
                                      .pushNamed(Routes.academicCalendar);
                                },
                              ),
                              const SizedBox(
                                height: 12,
                              ),
                              _buildEventList(
                                state.upcomingEvents,
                              ),
                            ],
                            const SizedBox(
                              height: 24,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                } else if (state is DashboardFetchFailure) {
                  return Padding(
                    padding: bodyContentPadding,
                    child: Center(
                      child: ErrorContainer(
                        errorMessageCode: state.errorMessage,
                        onTapRetry: () {
                          context.read<DashboardCubit>().fetchDashboard();
                        },
                      ),
                    ),
                  );
                } else {
                  return SingleChildScrollView(
                      padding: bodyContentPadding,
                      child: const HomeContainerShimmerContainer());
                }
              },
            ),
          ),
          const Align(
            alignment: Alignment.topCenter,
            child: HomeContainerAppBarContainer(),
          ),
        ],
      ),
    );
  }
}
