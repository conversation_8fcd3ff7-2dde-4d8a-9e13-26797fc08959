{"info": {"_postman_id": "9576e1c2-41dd-4312-be23-2e18e4d261ad", "name": "API Documentation Server Host", "description": "This template contains a boilerplate for documentation that you can quickly customize and reuse.\n\n### How to use this template:\n\n- Replace the content given brackets (()) with your API's details.\n- Tips are formatted in `codespan` - feel free to read and remove them.\n    \n\n---\n\n`Start with a brief overview of what your API offers.`\n\nThe ((product name)) provides many API products, tools, and resources that enable you to ((add product value here)).\n\n`You can also list the APIs you offer, link to the relevant pages, or do both in this section.`\n\n## **Getting started guide**\n\n`List the steps or points required to start using your APIs. Make sure to cover everything required to reach success with your API as quickly as possible.`\n\nTo start using the ((add APIs here)), you need to -\n\n`The points given below are from The Postman API's documentation. You can reference it to write your own getting started guide.`\n\n- You must use a valid API Key to send requests to the API endpoints. You can get your API key from Postman's [integrations dashboard](https://go.postman.co/settings/me/api-keys).\n- The API has [rate and usage limits](https://learning.postman.com/docs/developer/postman-api/postman-api-rate-limits/).\n- The API only responds to HTTPS-secured communications. Any requests sent via HTTP return an HTTP 301 redirect to the corresponding HTTPS resources.\n- The API returns request responses in JSON format. When an API request returns an error, it is sent in the JSON response as an error key.\n    \n\n## Authentication\n\n`Add details on the authorization keys/tokens required, steps that cover how to get them, and the relevant error codes.`\n\nThe ((product name)) API uses ((add your API's authorization type)) for authentication.\n\n`The details given below are from the Postman API's documentation. You can reference it to write your own authentication section.`\n\nPostman uses API keys for authentication. You can generate a Postman API key in the [API keys](https://postman.postman.co/settings/me/api-keys) section of your Postman account settings.\n\nYou must include an API key in each request to the Postman API with the X-Api-Key request header.\n\n### Authentication error response\n\nIf an API key is missing, malformed, or invalid, you will receive an HTTP 401 Unauthorized response code.\n\n## Rate and usage limits\n\n`Use this section to cover your APIs' terms of use. Include API limits, constraints, and relevant error codes, so consumers understand the permitted API usage and practices.`\n\n`The example given below is from The Postman API's documentation. Use it as a reference to write your APIs' terms of use.`\n\nAPI access rate limits apply at a per-API key basis in unit time. The limit is 300 requests per minute. Also, depending on your plan, you may have usage limits. If you exceed either limit, your request will return an HTTP 429 Too Many Requests status code.\n\nEach API response returns the following set of headers to help you identify your use status:\n\n| Header | Description |\n| --- | --- |\n| `X-RateLimit-Limit` | The maximum number of requests that the consumer is permitted to make per minute. |\n| `X-RateLimit-Remaining` | The number of requests remaining in the current rate limit window. |\n| `X-RateLimit-Reset` | The time at which the current rate limit window resets in UTC epoch seconds. |\n\n### 503 response\n\nAn HTTP `503` response from our servers indicates there is an unexpected spike in API access traffic. The server is usually operational within the next five minutes. If the outage persists or you receive any other form of an HTTP `5XX` error, [contact support](https://support.postman.com/hc/en-us/requests/new/).\n\n### **Need some help?**\n\n`Add links that customers can refer to whenever they need help.`\n\nIn case you have questions, go through our tutorials ((link to your video or help documentation here)). Or visit our FAQ page ((link to the relevant page)).\n\nOr you can check out our community forum, there’s a good chance our community has an answer for you. Visit our developer forum ((link to developer forum)) to review topics, ask questions, and learn from others.\n\n`You can also document or add links to libraries, code examples, and other resources needed to make a request.`", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "44937319", "_collection_link": "https://muham<PERSON>-el<PERSON><PERSON>.postman.co/workspace/E-School~ee6db2fd-cbb7-42ce-af3e-d4693cc214a4/collection/37272251-9576e1c2-41dd-4312-be23-2e18e4d261ad?action=share&source=collection_link&creator=44937319"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Register", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "<PERSON><PERSON><PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "Admin@123", "type": "text"}, {"key": "password_confirmation", "value": "Admin@123", "type": "text"}]}, "url": {"raw": "{{StudentUrl}}/register", "host": ["{{StudentUrl}}"], "path": ["register"]}, "description": "Gets information about the authenticated user."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"user\": {\n        \"id\": 12345678,\n        \"username\": \"taylor-lee\",\n        \"email\": \"<EMAIL>\",\n        \"fullName\": \"<PERSON>\",\n        \"avatar\": \"https://example.com/user/r5u9qpvmujfjf6lbqmga.jpg\",\n        \"isPublic\": true\n    },\n    \"operations\": [\n        {\n            \"name\": \"mock_usage\",\n            \"limit\": 1000000,\n            \"usage\": 110276,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"monitor_request_runs\",\n            \"limit\": 10000000,\n            \"usage\": 1141750,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"api_usage\",\n            \"limit\": 1000000,\n            \"usage\": 16240,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"custom_domains\",\n            \"limit\": 25,\n            \"usage\": 25,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"serverless_requests\",\n            \"limit\": 10000,\n            \"usage\": 0,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"integrations\",\n            \"limit\": 5000,\n            \"usage\": 1018,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"cloud_agent_requests\",\n            \"limit\": 1000000,\n            \"usage\": 1615,\n            \"overage\": 0\n        }\n    ]\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": \"rateLimited\",\n    \"message\": \"Rate limit exceeded. Please retry after **********\"\n}"}]}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "Admin@123", "type": "text"}]}, "url": {"raw": "{{StudentUrl}}/login", "host": ["{{StudentUrl}}"], "path": ["login"]}, "description": "Gets information about the authenticated user."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"user\": {\n        \"id\": 12345678,\n        \"username\": \"taylor-lee\",\n        \"email\": \"<EMAIL>\",\n        \"fullName\": \"<PERSON>\",\n        \"avatar\": \"https://example.com/user/r5u9qpvmujfjf6lbqmga.jpg\",\n        \"isPublic\": true\n    },\n    \"operations\": [\n        {\n            \"name\": \"mock_usage\",\n            \"limit\": 1000000,\n            \"usage\": 110276,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"monitor_request_runs\",\n            \"limit\": 10000000,\n            \"usage\": 1141750,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"api_usage\",\n            \"limit\": 1000000,\n            \"usage\": 16240,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"custom_domains\",\n            \"limit\": 25,\n            \"usage\": 25,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"serverless_requests\",\n            \"limit\": 10000,\n            \"usage\": 0,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"integrations\",\n            \"limit\": 5000,\n            \"usage\": 1018,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"cloud_agent_requests\",\n            \"limit\": 1000000,\n            \"usage\": 1615,\n            \"overage\": 0\n        }\n    ]\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": \"rateLimited\",\n    \"message\": \"Rate limit exceeded. Please retry after **********\"\n}"}]}, {"name": "Logout", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "1|IoU33jin1X9PXWJGW4v6XF7FEkvWdMtOSwYSHGxN", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "<PERSON><PERSON><PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "Admin@123", "type": "text"}, {"key": "password_confirmation", "value": "Admin@123", "type": "text"}]}, "url": {"raw": "{{StudentUrl}}/logout", "host": ["{{StudentUrl}}"], "path": ["logout"]}, "description": "Gets information about the authenticated user."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"user\": {\n        \"id\": 12345678,\n        \"username\": \"taylor-lee\",\n        \"email\": \"<EMAIL>\",\n        \"fullName\": \"<PERSON>\",\n        \"avatar\": \"https://example.com/user/r5u9qpvmujfjf6lbqmga.jpg\",\n        \"isPublic\": true\n    },\n    \"operations\": [\n        {\n            \"name\": \"mock_usage\",\n            \"limit\": 1000000,\n            \"usage\": 110276,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"monitor_request_runs\",\n            \"limit\": 10000000,\n            \"usage\": 1141750,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"api_usage\",\n            \"limit\": 1000000,\n            \"usage\": 16240,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"custom_domains\",\n            \"limit\": 25,\n            \"usage\": 25,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"serverless_requests\",\n            \"limit\": 10000,\n            \"usage\": 0,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"integrations\",\n            \"limit\": 5000,\n            \"usage\": 1018,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"cloud_agent_requests\",\n            \"limit\": 1000000,\n            \"usage\": 1615,\n            \"overage\": 0\n        }\n    ]\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": \"rateLimited\",\n    \"message\": \"Rate limit exceeded. Please retry after **********\"\n}"}]}, {"name": "Google Auth", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "redirect_uri", "value": "https://api.mahranstudio.com/api/student/auth/google/callback", "type": "string"}, {"key": "scope", "value": "email profile", "type": "string"}, {"key": "accessTokenUrl", "value": "https://oauth2.googleapis.com/token", "type": "string"}, {"key": "authUrl", "value": "https://accounts.google.com/o/oauth2/v2/auth", "type": "string"}, {"key": "useBrowser", "value": false, "type": "boolean"}, {"key": "tokenName", "value": "Google Oauth", "type": "string"}, {"key": "clientSecret", "value": "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", "type": "string"}, {"key": "clientId", "value": "************-kfmve42ti2lvknjp2s1q4cf04tuc7lhp.apps.googleusercontent.com", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [], "url": {"raw": "{{StudentUrl}}/auth/google/callback", "host": ["{{StudentUrl}}"], "path": ["auth", "google", "callback"]}}, "response": []}], "description": "The `/me` endpoints let you manage information about the authenticated user."}, {"name": "Email Verification", "item": [{"name": "<PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{StudentUrl}}/email/verify", "host": ["{{StudentUrl}}"], "path": ["email", "verify"]}, "description": "Gets information about the authenticated user."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"user\": {\n        \"id\": 12345678,\n        \"username\": \"taylor-lee\",\n        \"email\": \"<EMAIL>\",\n        \"fullName\": \"<PERSON>\",\n        \"avatar\": \"https://example.com/user/r5u9qpvmujfjf6lbqmga.jpg\",\n        \"isPublic\": true\n    },\n    \"operations\": [\n        {\n            \"name\": \"mock_usage\",\n            \"limit\": 1000000,\n            \"usage\": 110276,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"monitor_request_runs\",\n            \"limit\": 10000000,\n            \"usage\": 1141750,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"api_usage\",\n            \"limit\": 1000000,\n            \"usage\": 16240,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"custom_domains\",\n            \"limit\": 25,\n            \"usage\": 25,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"serverless_requests\",\n            \"limit\": 10000,\n            \"usage\": 0,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"integrations\",\n            \"limit\": 5000,\n            \"usage\": 1018,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"cloud_agent_requests\",\n            \"limit\": 1000000,\n            \"usage\": 1615,\n            \"overage\": 0\n        }\n    ]\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": \"rateLimited\",\n    \"message\": \"Rate limit exceeded. Please retry after **********\"\n}"}]}, {"name": "Resend OTP", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "1|BRFhXfyRur8GBXUEmwyLsJ7bC6jSPrMgmgFb2Jag", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "{{StudentUrl}}/resend/email/verify", "host": ["{{StudentUrl}}"], "path": ["resend", "email", "verify"]}, "description": "Gets information about the authenticated user."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"user\": {\n        \"id\": 12345678,\n        \"username\": \"taylor-lee\",\n        \"email\": \"<EMAIL>\",\n        \"fullName\": \"<PERSON>\",\n        \"avatar\": \"https://example.com/user/r5u9qpvmujfjf6lbqmga.jpg\",\n        \"isPublic\": true\n    },\n    \"operations\": [\n        {\n            \"name\": \"mock_usage\",\n            \"limit\": 1000000,\n            \"usage\": 110276,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"monitor_request_runs\",\n            \"limit\": 10000000,\n            \"usage\": 1141750,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"api_usage\",\n            \"limit\": 1000000,\n            \"usage\": 16240,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"custom_domains\",\n            \"limit\": 25,\n            \"usage\": 25,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"serverless_requests\",\n            \"limit\": 10000,\n            \"usage\": 0,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"integrations\",\n            \"limit\": 5000,\n            \"usage\": 1018,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"cloud_agent_requests\",\n            \"limit\": 1000000,\n            \"usage\": 1615,\n            \"overage\": 0\n        }\n    ]\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": \"rateLimited\",\n    \"message\": \"Rate limit exceeded. Please retry after **********\"\n}"}]}], "description": "The `/me` endpoints let you manage information about the authenticated user."}, {"name": "Reset Password", "item": [{"name": "Send Reset OTP", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": {"raw": "{{StudentUrl}}/send/otp/reset/password", "host": ["{{StudentUrl}}"], "path": ["send", "otp", "reset", "password"]}, "description": "Gets information about the authenticated user."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"user\": {\n        \"id\": 12345678,\n        \"username\": \"taylor-lee\",\n        \"email\": \"<EMAIL>\",\n        \"fullName\": \"<PERSON>\",\n        \"avatar\": \"https://example.com/user/r5u9qpvmujfjf6lbqmga.jpg\",\n        \"isPublic\": true\n    },\n    \"operations\": [\n        {\n            \"name\": \"mock_usage\",\n            \"limit\": 1000000,\n            \"usage\": 110276,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"monitor_request_runs\",\n            \"limit\": 10000000,\n            \"usage\": 1141750,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"api_usage\",\n            \"limit\": 1000000,\n            \"usage\": 16240,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"custom_domains\",\n            \"limit\": 25,\n            \"usage\": 25,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"serverless_requests\",\n            \"limit\": 10000,\n            \"usage\": 0,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"integrations\",\n            \"limit\": 5000,\n            \"usage\": 1018,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"cloud_agent_requests\",\n            \"limit\": 1000000,\n            \"usage\": 1615,\n            \"overage\": 0\n        }\n    ]\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": \"rateLimited\",\n    \"message\": \"Rate limit exceeded. Please retry after **********\"\n}"}]}, {"name": "Verify Reset OTP", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "otp", "value": "", "type": "text"}]}, "url": {"raw": "{{StudentUrl}}/verify/otp/reset/password", "host": ["{{StudentUrl}}"], "path": ["verify", "otp", "reset", "password"]}, "description": "Gets information about the authenticated user."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"user\": {\n        \"id\": 12345678,\n        \"username\": \"taylor-lee\",\n        \"email\": \"<EMAIL>\",\n        \"fullName\": \"<PERSON>\",\n        \"avatar\": \"https://example.com/user/r5u9qpvmujfjf6lbqmga.jpg\",\n        \"isPublic\": true\n    },\n    \"operations\": [\n        {\n            \"name\": \"mock_usage\",\n            \"limit\": 1000000,\n            \"usage\": 110276,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"monitor_request_runs\",\n            \"limit\": 10000000,\n            \"usage\": 1141750,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"api_usage\",\n            \"limit\": 1000000,\n            \"usage\": 16240,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"custom_domains\",\n            \"limit\": 25,\n            \"usage\": 25,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"serverless_requests\",\n            \"limit\": 10000,\n            \"usage\": 0,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"integrations\",\n            \"limit\": 5000,\n            \"usage\": 1018,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"cloud_agent_requests\",\n            \"limit\": 1000000,\n            \"usage\": 1615,\n            \"overage\": 0\n        }\n    ]\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": \"rateLimited\",\n    \"message\": \"Rate limit exceeded. Please retry after **********\"\n}"}]}, {"name": "Resend  OTP", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "", "type": "text"}, {"key": "password_confirmation", "value": "", "type": "text"}]}, "url": {"raw": "{{StudentUrl}}/reset/password", "host": ["{{StudentUrl}}"], "path": ["reset", "password"]}, "description": "Gets information about the authenticated user."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"user\": {\n        \"id\": 12345678,\n        \"username\": \"taylor-lee\",\n        \"email\": \"<EMAIL>\",\n        \"fullName\": \"<PERSON>\",\n        \"avatar\": \"https://example.com/user/r5u9qpvmujfjf6lbqmga.jpg\",\n        \"isPublic\": true\n    },\n    \"operations\": [\n        {\n            \"name\": \"mock_usage\",\n            \"limit\": 1000000,\n            \"usage\": 110276,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"monitor_request_runs\",\n            \"limit\": 10000000,\n            \"usage\": 1141750,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"api_usage\",\n            \"limit\": 1000000,\n            \"usage\": 16240,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"custom_domains\",\n            \"limit\": 25,\n            \"usage\": 25,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"serverless_requests\",\n            \"limit\": 10000,\n            \"usage\": 0,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"integrations\",\n            \"limit\": 5000,\n            \"usage\": 1018,\n            \"overage\": 0\n        },\n        {\n            \"name\": \"cloud_agent_requests\",\n            \"limit\": 1000000,\n            \"usage\": 1615,\n            \"overage\": 0\n        }\n    ]\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": \"rateLimited\",\n    \"message\": \"Rate limit exceeded. Please retry after **********\"\n}"}]}], "description": "The `/me` endpoints let you manage information about the authenticated user."}, {"name": "Users", "item": [{"name": "Soft Delete Profile", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "12|Mu8LfWKX535XpRU61HqYXdFzLjnyUlgvciq0HAM5", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "_method", "value": "delete", "type": "text"}, {"key": "password", "value": "superadmin", "type": "text"}]}, "url": {"raw": "{{StudentUrl}}/delete/user/profile", "host": ["{{StudentUrl}}"], "path": ["delete", "user", "profile"]}, "description": "Deletes a collection."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "https://api.getpostman.com/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "12ece9e1-2abf-4edc-8e34-de66e74114d2"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"collection\": {\n        \"id\": \"12ece9e1-2abf-4edc-8e34-de66e74114d2\",\n        \"uid\": \"12345678-12ece9e1-2abf-4edc-8e34-de66e74114d2\"\n    }\n}"}, {"name": "Not Found", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "https://api.getpostman.com/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "12ece9e1-2abf-4edc-8e34-de66e74114d2"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"instanceNotFoundError\",\n        \"message\": \"The specified item does not exist.\",\n        \"details\": {\n            \"item\": \"collection\",\n            \"id\": \"12ece9e1-2abf-4edc-8e34-de66e74114d2\"\n        }\n    }\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "https://api.getpostman.com/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "12ece9e1-2abf-4edc-8e34-de66e74114d2"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": \"rateLimited\",\n    \"message\": \"Rate limit exceeded. Please retry after **********\"\n}"}]}, {"name": "Update Profile", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "9|WKWN0VQPIZAVj6hMqeW1RMeIOhMKwujqesYDsnUS", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "_method", "value": "patch", "type": "text"}, {"key": "name", "value": "ahmed", "type": "text"}, {"key": "image", "type": "file", "src": "/home/<USER>/Desktop/1K9dqQVp0xZ9nbqwURFB60FiHSsHY4Grd2Yq1Sat.jpg", "disabled": true}]}, "url": {"raw": "{{StudentUrl}}/update/user/profile", "host": ["{{StudentUrl}}"], "path": ["update", "user", "profile"]}, "description": "Updates a collection using the [Postman Collection v2 schema format](https://schema.postman.com/json/collection/v2.1.0/docs/index.html). Include a `collection` object in the request body that contains the following required properties:\n\n- `info` — An **object** that contains the following properties:\n    - `name` — A **string** value that contains the collection's name.\n    - `schema` — A **string** that contains a URL to the collection's schema. For example, the `https://schema.getpostman.com/collection/v1` URL.\n- `item` — An **object** that contains the HTTP request and response information.\n    - `request` — An **object** that contains the collection's request information. For a complete list of values, refer to the `definitions.request` entry in the [collection.json schema file](https://schema.postman.com/json/collection/v2.1.0/collection.json). If you pass an empty object for this value, the system defaults to an untitled GET request.\n\n**Note:**\n\n- For a complete list of available property values for this endpoint, use the following references available in the [collection.json schema file](https://schema.postman.com/json/collection/v2.1.0/collection.json):\n    - `info` object — Use the `definitions.info` entry.\n    - `item` object — Use the `definitions.items` entry.\n- For all other possible values, refer to the [collection.json schema file](https://schema.postman.com/json/collection/v2.1.0/collection.json).\n    \n\n### Important\n\nUse caution when using this endpoint. The system will replace the existing collection with the values passed in the request body."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collection\": {\n        \"info\": {\n            \"name\": \"Test Collection\",\n            \"description\": \"This collection makes a request to the Postman Echo service to get a list of request headers sent by an HTTP client.\",\n            \"schema\": \"https://schema.getpostman.com/json/collection/v2.1.0/collection.json\"\n        },\n        \"item\": [\n            {\n                \"name\": \"Test POST Response\",\n                \"event\": [\n                    {\n                        \"listen\": \"test\",\n                        \"script\": {\n                            \"id\": \"7d2334fc-a84a-4c3d-b26c-7529afa4c0ae\",\n                            \"exec\": [\n                                \"pm.test(\\\"Status code is 200\\\", function () {\",\n                                \"    pm.response.to.have.status(200);\",\n                                \"});\"\n                                ],\n                            \"type\": \"text/javascript\"\n                            }\n                        }\n                    ],\n                \"request\": {\n                    \"url\": \"https://echo.getpostman.com/headers\",\n                    \"method\": \"POST\",\n                    \"header\": [\n                        {\n                            \"key\": \"Content-Type\",\n                            \"value\": \"application/json\"\n                        }\n                    ]\n                }\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.getpostman.com/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "12ece9e1-2abf-4edc-8e34-de66e74114d2"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"collection\": {\n        \"id\": \"12ece9e1-2abf-4edc-8e34-de66e74114d2\",\n        \"name\": \"Test Collection\",\n        \"uid\": \"12345678-12ece9e1-2abf-4edc-8e34-de66e74114d2\"\n    }\n}"}, {"name": "Forbidden", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collection\": {\n        \"info\": {\n            \"name\": \"Test Collection\",\n            \"description\": \"This collection makes a request to the Postman Echo service to get a list of request headers sent by an HTTP client.\",\n            \"schema\": \"https://schema.getpostman.com/json/collection/v2.1.0/collection.json\"\n        },\n        \"item\": [\n            {\n                \"name\": \"Test POST Response\",\n                \"event\": [\n                    {\n                        \"listen\": \"test\",\n                        \"script\": {\n                            \"id\": \"7d2334fc-a84a-4c3d-b26c-7529afa4c0ae\",\n                            \"exec\": [\n                                \"pm.test(\\\"Status code is 200\\\", function () {\",\n                                \"    pm.response.to.have.status(200);\",\n                                \"});\"\n                                ],\n                            \"type\": \"text/javascript\"\n                            }\n                        }\n                    ],\n                \"request\": {\n                    \"url\": \"https://echo.getpostman.com/headers\",\n                    \"method\": \"POST\",\n                    \"header\": [\n                        {\n                            \"key\": \"Content-Type\",\n                            \"value\": \"application/json\"\n                        }\n                    ]\n                }\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.getpostman.com/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "12ece9e1-2abf-4edc-8e34-de66e74114d2"]}}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"forbiddenError\",\n        \"message\": \"You do not have enough permissions to perform this action.\"\n    }\n}"}, {"name": "Not Found", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collection\": {\n        \"info\": {\n            \"name\": \"Test Collection\",\n            \"description\": \"This collection makes a request to the Postman Echo service to get a list of request headers sent by an HTTP client.\",\n            \"schema\": \"https://schema.getpostman.com/json/collection/v2.1.0/collection.json\"\n        },\n        \"item\": [\n            {\n                \"name\": \"Test POST Response\",\n                \"event\": [\n                    {\n                        \"listen\": \"test\",\n                        \"script\": {\n                            \"id\": \"7d2334fc-a84a-4c3d-b26c-7529afa4c0ae\",\n                            \"exec\": [\n                                \"pm.test(\\\"Status code is 200\\\", function () {\",\n                                \"    pm.response.to.have.status(200);\",\n                                \"});\"\n                                ],\n                            \"type\": \"text/javascript\"\n                            }\n                        }\n                    ],\n                \"request\": {\n                    \"url\": \"https://echo.getpostman.com/headers\",\n                    \"method\": \"POST\",\n                    \"header\": [\n                        {\n                            \"key\": \"Content-Type\",\n                            \"value\": \"application/json\"\n                        }\n                    ]\n                }\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.getpostman.com/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "12ece9e1-2abf-4edc-8e34-de66e74114d2"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"instanceNotFoundError\",\n        \"message\": \"The specified item does not exist.\",\n        \"details\": {\n            \"item\": \"collection\",\n            \"id\": \"12ece9e1-2abf-4edc-8e34-de66e74114d2\"\n        }\n    }\n}"}, {"name": "Malformed Request", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collection\": {\n        \"info\": {\n            \"name\": \"Test Collection\"\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.getpostman.com/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "12ece9e1-2abf-4edc-8e34-de66e74114d2"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"malformedRequestError\",\n        \"message\": \"Found 2 errors with the supplied collection.\",\n        \"details\": [\n            \": must have required property 'item'\",\n            \"info: must have required property 'schema'\"\n        ]\n    }\n}"}, {"name": "Collection ID Mismatch", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collection\": {\n        \"info\": {\n            \"name\": \"Test Collection\",\n            \"description\": \"This collection makes a request to the Postman Echo service to get a list of request headers sent by an HTTP client.\",\n            \"schema\": \"https://schema.getpostman.com/json/collection/v2.1.0/collection.json\"\n        },\n        \"item\": [\n            {\n                \"name\": \"Test POST Response\",\n                \"event\": [\n                    {\n                        \"listen\": \"test\",\n                        \"script\": {\n                            \"id\": \"7d2334fc-a84a-4c3d-b26c-7529afa4c0ae\",\n                            \"exec\": [\n                                \"pm.test(\\\"Status code is 200\\\", function () {\",\n                                \"    pm.response.to.have.status(200);\",\n                                \"});\"\n                                ],\n                            \"type\": \"text/javascript\"\n                            }\n                        }\n                    ],\n                \"request\": {\n                    \"url\": \"https://echo.getpostman.com/headers\",\n                    \"method\": \"POST\",\n                    \"header\": [\n                        {\n                            \"key\": \"Content-Type\",\n                            \"value\": \"application/json\"\n                        }\n                    ]\n                }\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.getpostman.com/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "12ece9e1-2abf-4edc-8e34-de66e74114d2"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"collectionMismatchError\",\n        \"message\": \"The collection ID in the path does not match the collection ID in the request body.\"\n    }\n}"}, {"name": "Rate Limit Exceeded", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"collection\": {\n        \"info\": {\n            \"name\": \"Test Collection\",\n            \"description\": \"This collection makes a request to the Postman Echo service to get a list of request headers sent by an HTTP client.\",\n            \"schema\": \"https://schema.getpostman.com/json/collection/v2.1.0/collection.json\"\n        },\n        \"item\": [\n            {\n                \"name\": \"Test POST Response\",\n                \"event\": [\n                    {\n                        \"listen\": \"test\",\n                        \"script\": {\n                            \"id\": \"7d2334fc-a84a-4c3d-b26c-7529afa4c0ae\",\n                            \"exec\": [\n                                \"pm.test(\\\"Status code is 200\\\", function () {\",\n                                \"    pm.response.to.have.status(200);\",\n                                \"});\"\n                                ],\n                            \"type\": \"text/javascript\"\n                            }\n                        }\n                    ],\n                \"request\": {\n                    \"url\": \"https://echo.getpostman.com/headers\",\n                    \"method\": \"POST\",\n                    \"header\": [\n                        {\n                            \"key\": \"Content-Type\",\n                            \"value\": \"application/json\"\n                        }\n                    ]\n                }\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.getpostman.com/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "12ece9e1-2abf-4edc-8e34-de66e74114d2"]}}, "status": "Too Many Requests", "code": 429, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": {"content": "", "type": "text/plain"}}], "cookie": [], "body": "{\n    \"error\": \"rateLimited\",\n    \"message\": \"Rate limit exceeded. Please retry after **********\"\n}"}]}], "description": "The `/collections` endpoints let you manage your [collections](https://learning.postman.com/docs/sending-requests/intro-to-collections/)."}, {"name": "Marketplace Item", "item": [{"name": "Get All Package", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "2|mGLqDC1FnZ6y8dXx23HRhH4g4eCUztnzbP2RRujD", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{StudentUrl}}/packages", "host": ["{{StudentUrl}}"], "path": ["packages"]}}, "response": []}, {"name": "Get All Assets", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "2|mGLqDC1FnZ6y8dXx23HRhH4g4eCUztnzbP2RRujD", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{StudentUrl}}/digital-assets", "host": ["{{StudentUrl}}"], "path": ["digital-assets"]}}, "response": []}, {"name": "Get All Subjects", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "2|mGLqDC1FnZ6y8dXx23HRhH4g4eCUztnzbP2RRujD", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{StudentUrl}}/subjects", "host": ["{{StudentUrl}}"], "path": ["subjects"]}}, "response": []}]}], "auth": {"type": "apikey", "apikey": [{"key": "key", "value": "X-API-Key", "type": "string"}, {"key": "value", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "StudentUrl", "value": "https://api.mahranstudio.com/api/student"}, {"key": "token", "value": "", "type": "default"}]}