{"buildFiles": ["C:\\Users\\<USER>\\Documents\\vs code\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\vs code\\ecenter\\eschool\\eschool_teacher\\android\\app\\.cxx\\Debug\\2v4g9521\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\vs code\\ecenter\\eschool\\eschool_teacher\\android\\app\\.cxx\\Debug\\2v4g9521\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}