class ZoomMeeting {
  final String id;
  final String meetingId;
  final String password;
  final String joinUrl;
  final DateTime startTime;
  final DateTime endTime;
  final String lessonId;
  
  ZoomMeeting({
    required this.id,
    required this.meetingId,
    required this.password,
    required this.joinUrl,
    required this.startTime,
    required this.endTime,
    required this.lessonId,
  });
  
  factory ZoomMeeting.fromJson(Map<String, dynamic> json) {
    return ZoomMeeting(
      id: json['id'] as String? ?? '',
      meetingId: json['meeting_id'] as String? ?? '',
      password: json['password'] as String? ?? '',
      joinUrl: json['join_url'] as String? ?? '',
      startTime: json['start_time'] != null 
          ? DateTime.parse(json['start_time'] as String)
          : DateTime.now(),
      endTime: json['end_time'] != null 
          ? DateTime.parse(json['end_time'] as String)
          : DateTime.now().add(const Duration(hours: 1)),
      lessonId: json['lesson_id'] as String? ?? '',
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'meeting_id': meetingId,
      'password': password,
      'join_url': joinUrl,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'lesson_id': lessonId,
    };
  }
} 