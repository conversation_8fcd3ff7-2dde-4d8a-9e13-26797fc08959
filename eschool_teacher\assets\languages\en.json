{"email": "Email", "password": "Password", "letsSignIn": "Let's Sign in", "welcomeBack": "Welcome Back", "youHaveBeenMissed": "You've been missed!", "signIn": "Sign in", "home": "Home", "schedule": "Schedule", "profile": "Profile", "setting": "Settings", "myClasses": "My Classes", "classTeacher": "Class Teacher", "information": "Information", "assignments": "Assignments", "studyMaterials": "Study Materials", "announcements": "Announcements", "teacher": "Teacher", "personalDetails": "Personal Details", "name": "Name", "dateOfBirth": "Date of Birth", "phoneNumber": "Phone Number", "logout": "Logout", "appVersion": "App Version", "address": "Address", "class": "Class", "chapters": "Lessons", "announcement": "Announcement", "chapterName": "Lesson name", "chapterDescription": "Lesson description", "createChapter": "C<PERSON> <PERSON><PERSON>", "createAnnouncement": "Create Announcement", "announcementDescription": "Announcement Description", "addAttatchments": "Add Attachments", "subjectName": "Subject Name", "dueDate": "Due Date", "assignedDate": "Assigned Date", "instructions": "Instructions", "referenceMaterials": "Reference Materials", "points": "Points", "lateSubmission": "Late Submission", "resubmissionOfRejectedAssignment": "Resubmission Of Rejected Assignment", "extraDaysForRejectedAssignment": "Extra days for resubmission of rejected assignment", "delete": "Delete", "edit": "Edit", "view": "View", "all": "All", "completed": "Completed", "submitted": "Submitted", "reSubmitted": "Resubmitted", "accepted": "Accepted", "rejected": "Rejected", "pending": "Pending", "reject": "Reject", "accept": "Accept", "download": "Download", "addRemark": "Add remark", "submit": "Submit", "submitting": "Submitting", "fileDownload": "File Download", "createAssignment": "Create Assignment", "editassignment": "Edit Assignment", "assignmentName": "Assignment Name", "dueTime": "Due Time", "videos": "Videos", "files": "Files", "uploadFile": "Upload File", "uploadVideo": "Upload Video", "videoTitle": "Video Title", "videoDescription": "Video Description", "videoThumbnailImage": "Thumbnail Image", "selectChapter": "Select Chapter", "videoYoutubeLink": "Youtube Link", "uploadVideos": "Upload Videos", "addVideo": "Add Video", "uploadFiles": "Upload Files", "addFile": "Add File", "fileName": "File Name", "students": "Students", "subjects": "Subjects", "subjectCode": "Code", "searchStudent": "Search student", "noStudentsFound": "No students found", "takeAttendance": "Take Attendance", "present": "Present", "absent": "Absent", "attendanceSummary": "Attendance Summary", "totalPresent": "Total Present", "totalAbsent": "Total Absent", "details": "Details", "results": "Results", "grNumber": "GR Number", "sub": "Sub", "marks": "Marks", "total": "Total", "grade": "Grade", "date": "Date", "obtained": "Obtained", "percentage": "Percentage", "result": "Result", "addResult": "Add Result", "obtainedMarks": "Obtained Marks", "submitResult": "Submit Result", "viewResult": "View Result", "attendance": "Attendance", "forgotPassword": "Forgot password", "termAndConditionAgreement": "By logging in, you agree to our", "termsAndCondition": "Terms & Condition", "privacyPolicy": "Privacy Policy", "pleaseEnterEmail": "Please enter email", "pleaseEnterExtraDaysForResubmission": "Please Enter Extra Days For Resubmission", "passwordUpdateLinkSent": "Password update link sent to", "canNotSendResetPasswordRequest": "Can not request reset password. Try again later :(", "featureDisableInDemoVersion": "This feature is disable in demo version", "pleaseEnterPassword": "Please enter password", "gender": "Gender", "qualification": "Qualification", "currentAddress": "Current Address", "permanentAddress": "Permanent Address", "retry": "Retry", "defaultErrorMessage": "Something went wrong. Please try again later!", "noInternet": "No internet! Please check your network connection and try again.", "invalidLogInCredentials": "Invalid LogIn Credentials", "invalidUserDetails": "Invalid user details", "invalidPassword": "Invalid password", "selectFile": "Select File", "topics": "Topics", "addLesson": "<PERSON><PERSON>", "lessons": "Lessons", "fetchingSubjects": "Fetching subjects", "youtubeLink": "Youtube link", "fileUpload": "File upload", "videoUpload": "Video upload", "selectThumbnail": "Select thumbnail", "selectVideo": "Select video", "addStudyMaterial": "Add study material", "studyMaterialName": "Study material name", "filePath": "File", "thumbnailImage": "Thumbnail image", "pleaseEnterStudyMaterialName": "Please enter study material name", "pleaseSelectFile": "Please select file", "pleaseSelectThumbnailImage": "Please select thumbnail image", "pleaseEnterYoutubeLink": "Please enter youtube link", "pleaseSelectVideo": "Please select video", "permissionToPickFile": "Please give permission to pick file", "pleseEnterAssignmentName": "Please enter Assignment name", "pleaseSelectDate": "Please Select Date", "pointsLength": "Please Enter Point Length Less Then 10", "plseaseSelectTime": "PLease Select Time", "pleaseEnterLessPoints": "Not Assign Point higher to Assignment Points", "pleaseEnterRemark": "Please Enter Remark", "pleaseEnterPoints": "Please Enter Points", "pleaseEnterLessonName": "Please enter Lesson name", "pleaseEnterLessonDescription": "Please enter Lesson description", "pleaseSelectSubject": "Please select subject", "lessonAdded": "Lesson added successfully", "sucessfullyCreateAssignment": "Successfully Created Assignment", "failureReviewAssignmet": "Review Assignment Failure", "reviewAssignmentSucessfully": "Review Assignment Successfully", "editsucessfullassignment": "Assignment Edited Successfully", "unableToDeleteLesson": "Unable to delete Lesson", "editLesson": "<PERSON>", "editStudyMaterial": "Edit study material", "unableToOpenFile": "Unable to open the file", "fetchingLessons": "Fetching Lessons", "attachments": "Attachment(s)", "unableToDeleteTopic": "Unable to delete Topic", "unableToDeleteAssignment": "Unable to delete Assignment", "topicName": "Topic Name", "topicDescription": "Topic Description", "noLessons": "No Lessons", "addTopic": "Add Topic", "editTopic": "Edit Topic", "oldFilesWillBeReplacedWithLatestOne": "Old file will be replaced with latest one", "pleaseEnterTopicName": "Please enter Topic name", "pleaseEnterTopicDescription": "Please enter Topic description", "pleaseSelectLesson": "Please select Lesson", "topicAddedSuccessfully": "Topic added successfully", "more": "More", "aboutUs": "About Us", "faqs": "FAQs", "rateUs": "Rate Us", "contactUs": "Contact us", "share": "Share", "changePassword": "Change password", "currentPassword": "Current password", "newPassword": "New password", "confirmNewPassword": "Confirm new password", "pleaseEnterAllField": "Please enter all field", "newPasswordAndConfirmSame": "New password and confirm password must be same", "passwordChangedSuccessfully": "Password changed successfully", "appLanguage": "Application language", "yes": "Yes", "no": "No", "permissionsNotGiven": "Please give permission to manage files in external storage", "fileDownloadedSuccessfully": "File downloaded successfully", "unableToOpen": "Unable to open file", "cancel": "Cancel", "downloadingFile": "Downloading File", "failedToDownload": "Failed to download. Try again later", "addAnnouncement": "Add Announcement", "editAnnouncement": "Edit Announcement", "pleaseAddAnnouncementTitle": "Please add Announcement title", "announcementTitle": "Announcement title", "announcementAdded": "Announcement added successfully", "unableToDelete": "Unable to delete file", "nameAlreadyTaken": "The name has already been taken", "maintenance": "Under Maintenance", "appUnderMaintenance": "We're currently performing essential updates to enhance your app experience. Please check back shortly.", "update": "Update", "updateAvailable": "Update Available", "newUpdateAvailable": "A new and improved version of the app is now available. Update now for the best experience.", "Topic": "Topic(s)", "studentDetails": "Student details", "rollNo": "Roll No.", "dob": "<PERSON><PERSON>", "bloodGrp": "Blood Grp.", "grNo": "GR No.", "occupation": "Occupation", "phone": "Phone", "mother": "Mother", "father": "Father", "guardian": "Guardian", "holiday": "Holiday", "attendanceNotViewEdit": "Because of holiday can not view/take attendance", "attendanceSubmittedSuccessfully": "Attendance submitted successfully", "todayAttendance": "Today's attendance", "holidays": "Holidays", "noAnnouncements": "No Announcements Found", "noTopics": "No Topics Found", "noLectures": "No Lectures Scheduled", "noAssignments": "No Assignments Found", "exams": "<PERSON><PERSON>", "allExams": "All Exams", "upComing": "Upcoming", "onGoing": "Ongoing", "examName": "Exam Name", "noExamsFound": "Yee.. No Exam found!", "noExamTimeTableFound": "No Timetable Added", "examTimeTable": "Exam Timetable", "fetchingExams": "Fetching exams", "noExams": "No Exams Found", "noSubjects": "No Subjects Found", "marksAddedSuccessfully": "Marks Added Successfully", "marksMoreThanTotalMarks": "Marks Should not more than Total Marks", "pleaseEnterSomeData": "Please... Enter some data", "noDataFound": "No Data Found..!", "pleaseEnterAllMarks": "Please Enter All Marks", "noClassAssigned": "No class assigned to you yet!", "shift": "Shift", "medium": "Medium", "noSubjectsInClass": "No subject assigned to you for this class yet!", "allowStoragePermissionToContinue": "Allow storage permission to continue", "noSubjectSelected": "Please select a subject", "noLessonSelected": "Please select a Lesson", "internetServerError": "We apologize, but our service is temporarily unavailable due to maintenance or high traffic. Please check back in a little while.", "noStudentsInClass": "No students are added in this class yet!", "fileDownloadingFailed": "File download failed. The file has been removed from the server.", "noNotifications": "No Notifications Found!", "notifications": "Notifications", "topic": "Topic", "pdfFileLoading": "PDF file loading...", "pdfFileOpenError": "Unable to open PDF file", "imageFileOpenError": "Unable to open image file", "imageFileLoading": "Image file loading...", "showMore": "Show More", "showLess": "Show Less", "chat": "Cha<PERSON>", "noUsersToChatWith": "Op<PERSON>, there are no users to chat with!", "noChatsWithUser": "You've not done any chat with this user, start chatting now...", "chatSendHint": "Type your message", "document": "Document", "camera": "Camera", "gallery": "Gallery", "yesterday": "Yesterday", "loadingMoreChats": "Loading More Chats", "errorLoadingMoreChatsRetry": "Error Loading More Chats, <PERSON><PERSON> to retry.", "errorSendingMessageRetry": "Error Sending this message, <PERSON><PERSON> to retry.", "selectedFiles": "Selected Files", "cameraImages": "Camera Images", "pickedImages": "Picked Images", "send": "Send", "subjectTeacher": "Subject Teacher", "errorLoadingMoreTapToRetry": "Error loading more data, Tap to Retry", "removed": "Removed", "maxAllowedFilesAre": "Max allowed files are", "filesAsTheyExceededTheLimitOf": "files as they exceeded the limit of", "today": "Today", "extra": "extra", "parents": "Parents", "children": "Children", "childrenDetails": "Children Details", "filesReceived": "Files Received", "noUsersFound": "No users found!", "searchByUsernameAbove": "Please enter user name in searchbox above to see the results here...", "addMoreCharactersToSearch": "Please add 3 or more characters to search", "searchHint": "Enter user's name here to search", "logoutDialogMessage": "Are you sure you want to logout?", "deleteDialogTitle": "Delete", "deleteDialogMessage": "Are you sure you want to delete this item?", "events": "Events", "viewDetails": "View Details", "day": "Day", "academicCalendar": "Academic Calendar", "noEvents": "No Events Found!", "to": "to", "otherDetails": "Other Details", "viewFile": "View File", "olderMessagesThen": "Messages older then", "daysWillBeDeletedAutomatically": "days will be deleted automatically.", "textSubmission": "Text Submission", "manageLeaves": "Manage Leaves", "noLeavesFound": "No Leaves Found", "submitLeaveRequestsEasilyAndKeepYourAdministrationInformation": "Submit Leave Requests Easily and Keep Your Administration Information", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "unknown": "Unknown", "addLeave": "Add Leave", "reason": "Reason", "reasonRequired": "Reason is required", "fromDate": "From Date", "toDate": "To Date", "pickDateRangeToSubmit": "Pick Date Range to Submit", "leaveRequestAddedSuccessfully": "Leave request added successfully", "submitLeave": "Submit Leave", "full": "Full", "firstHalf": "First Half", "secondHalf": "Second Half", "filterBySessionYear": "Filter by: Session Year", "filterByMonth": "Filter by: Month", "allowedLeaves": "Allowed Leaves", "leavesTaken": "Leaves Taken", "leaveDate": "Leave Date", "status": "Status", "leaveReason": "Leave Reason", "totalDays": "Total Days", "viewMore": "View More", "leaveDetails": "Leave Details", "leaveTiming": "Leave Timing", "pickValidDateRangeWithoutHolidays": "Pick valid date range other then holidays to submit", "noHolidaysThisMonth": "No holidays this month", "noEventsThisMonth": "No events this month", "noExamsThisMonth": "No exams this month", "topicEditedSuccessfully": "<PERSON><PERSON> edited successfully", "lessonEditedSuccessfully": "<PERSON><PERSON> edited successfully", "announcementEditedSuccessfully": "Announcement edited successfully", "manageStudentLeaves": "Student Leaves", "filterByClass": "Filter by: Class", "rejectConfirmationMsg": "Are you sure you want to Reject Leave ?", "acceptConfirmationMsg": "Are you sure you want to Accept Leave ?", "rejectedSuccessMsg": "Leave request has been rejected successfully", "acceptedSuccessMsg": "Leave request has been accepted successfully", "confirm": "Confirm", "optional": "Optional", "addNote": "Add Note", "back": "Back", "leaveRequests": "Leave Requests", "allMonths": "All Months", "studentInLeave": "On Leave", "exploreAcademics": "Explore Academics", "upcomingEvents": "Upcoming Events", "viewAll": "View All", "todaysTimetable": "Today's Timetable", "viewLess": "View Less", "subject": "Subject", "addTimetableLink": "Add Timetable Link", "editTimetableLink": "Edit Timetable Link", "linkTitle": "Custom Link Title", "liveClassLink": "Live Class/Custom Link URL", "enterValidLink": "Enter a valid link URL", "fillBothFields": "Fill both the fields to submit.", "clear": "Clear", "timetableSlotLinkSuccessfullyUpdated": "Timetable slot link successfully updated!", "upcomingExams": "Upcoming Exams", "staffLeaves": "Staff Leaves", "tomorrow": "Tomorrow", "noStaffOnLeave": "No staff member is on leave", "role": "Role", "studentPendingLeaveRequests": "Recent Leave Requests", "leaveRequestStatusUpdatedSuccessfully": "Leave request status updated successfully"}