import 'package:eschool_teacher/data/models/transaction.dart';

class Earnings {
  final double totalEarnings;
  final double availableBalance;
  final double pendingAmount;
  final int lessonsTaught;
  final int lessonsPaid;
  final int lessonsPending;
  final List<Transaction> transactions;

  Earnings({
    required this.totalEarnings,
    required this.availableBalance,
    required this.pendingAmount,
    required this.lessonsTaught,
    required this.lessonsPaid,
    required this.lessonsPending,
    required this.transactions,
  });

  factory Earnings.fromJson(Map<String, dynamic> json) {
    return Earnings(
      totalEarnings: (json['total_earnings'] as num?)?.toDouble() ?? 0.0,
      availableBalance: (json['available_balance'] as num?)?.toDouble() ?? 0.0,
      pendingAmount: (json['pending_amount'] as num?)?.toDouble() ?? 0.0,
      lessonsTaught: json['lessons_taught'] as int? ?? 0,
      lessonsPaid: json['lessons_paid'] as int? ?? 0,
      lessonsPending: json['lessons_pending'] as int? ?? 0,
      transactions: json['transactions'] != null
          ? List<Transaction>.from(
              (json['transactions'] as List).map(
                (transactionJson) => Transaction.fromJson(transactionJson),
              ),
            )
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_earnings': totalEarnings,
      'available_balance': availableBalance,
      'pending_amount': pendingAmount,
      'lessons_taught': lessonsTaught,
      'lessons_paid': lessonsPaid,
      'lessons_pending': lessonsPending,
      'transactions': transactions.map((transaction) => transaction.toJson()).toList(),
    };
  }

  // Empty earnings object for initial state
  factory Earnings.empty() {
    return Earnings(
      totalEarnings: 0.0,
      availableBalance: 0.0,
      pendingAmount: 0.0,
      lessonsTaught: 0,
      lessonsPaid: 0,
      lessonsPending: 0,
      transactions: [],
    );
  }
} 