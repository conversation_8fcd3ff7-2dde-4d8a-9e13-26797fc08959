import 'package:flutter/material.dart';
import '../../tokens/colors.dart';
import '../../tokens/spacing.dart';
import '../../tokens/typography.dart';
import '../../tokens/shadows.dart';

/// Navigation item data
class AppBottomNavigationItem {
  const AppBottomNavigationItem({
    required this.icon,
    required this.label,
    this.activeIcon,
    this.badge,
  });

  /// Icon for unselected state
  final Widget icon;

  /// Icon for selected state (defaults to icon)
  final Widget? activeIcon;

  /// Label text
  final String label;

  /// Badge widget (e.g., notification count)
  final Widget? badge;
}

/// Shared bottom navigation bar following the design system
class AppBottomNavigationBar extends StatelessWidget {
  const AppBottomNavigationBar({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.elevation,
    this.showLabels = true,
    this.type = BottomNavigationBarType.fixed,
    this.iconSize,
    this.selectedFontSize,
    this.unselectedFontSize,
    this.height,
    this.margin,
    this.borderRadius,
    this.showBorder = true,
  });

  /// Navigation items
  final List<AppBottomNavigationItem> items;

  /// Currently selected index
  final int currentIndex;

  /// Callback when item is tapped
  final ValueChanged<int> onTap;

  /// Background color
  final Color? backgroundColor;

  /// Selected item color
  final Color? selectedItemColor;

  /// Unselected item color
  final Color? unselectedItemColor;

  /// Elevation
  final double? elevation;

  /// Whether to show labels
  final bool showLabels;

  /// Navigation bar type
  final BottomNavigationBarType type;

  /// Icon size
  final double? iconSize;

  /// Selected label font size
  final double? selectedFontSize;

  /// Unselected label font size
  final double? unselectedFontSize;

  /// Navigation bar height
  final double? height;

  /// External margin
  final EdgeInsetsGeometry? margin;

  /// Border radius
  final double? borderRadius;

  /// Whether to show top border
  final bool showBorder;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Determine colors
    final effectiveBackgroundColor = backgroundColor ?? AppColors.surface;
    final effectiveSelectedColor = selectedItemColor ?? AppColors.primary;
    final effectiveUnselectedColor = unselectedItemColor ?? AppColors.onSurfaceVariant;
    
    return Container(
      margin: margin,
      height: height ?? AppSpacing.bottomNavHeight,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: borderRadius != null 
            ? BorderRadius.circular(borderRadius!)
            : null,
        border: showBorder 
            ? Border(
                top: BorderSide(
                  color: AppColors.border,
                  width: 1,
                ),
              )
            : null,
        boxShadow: elevation != null && elevation! > 0
            ? AppShadows.custom(elevation: elevation!)
            : AppShadows.elevation1,
      ),
      child: SafeArea(
        top: false,
        child: _buildNavigationContent(
          effectiveSelectedColor,
          effectiveUnselectedColor,
        ),
      ),
    );
  }

  Widget _buildNavigationContent(
    Color selectedColor,
    Color unselectedColor,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: items.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final isSelected = index == currentIndex;

        return _buildNavigationItem(
          item,
          index,
          isSelected,
          selectedColor,
          unselectedColor,
        );
      }).toList(),
    );
  }

  Widget _buildNavigationItem(
    AppBottomNavigationItem item,
    int index,
    bool isSelected,
    Color selectedColor,
    Color unselectedColor,
  ) {
    final effectiveIconSize = iconSize ?? AppSpacing.iconMd;
    final itemColor = isSelected ? selectedColor : unselectedColor;
    
    // Build icon with optional badge
    Widget iconWidget = Stack(
      clipBehavior: Clip.none,
      children: [
        IconTheme(
          data: IconThemeData(
            color: itemColor,
            size: effectiveIconSize,
          ),
          child: isSelected && item.activeIcon != null 
              ? item.activeIcon!
              : item.icon,
        ),
        if (item.badge != null)
          Positioned(
            top: -4,
            right: -4,
            child: item.badge!,
          ),
      ],
    );

    // Build the complete item
    Widget navigationItem = InkWell(
      onTap: () => onTap(index),
      borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.xs,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            iconWidget,
            if (showLabels) ...[
              const SizedBox(height: AppSpacing.xs),
              Text(
                item.label,
                style: AppTypography.labelSmall.copyWith(
                  color: itemColor,
                  fontSize: isSelected 
                      ? (selectedFontSize ?? AppTypography.labelSmall.fontSize)
                      : (unselectedFontSize ?? AppTypography.labelSmall.fontSize),
                  fontWeight: isSelected 
                      ? FontWeight.w600 
                      : FontWeight.w400,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );

    return Expanded(
      child: navigationItem,
    );
  }
}

/// Badge widget for navigation items
class NavigationBadge extends StatelessWidget {
  const NavigationBadge({
    super.key,
    this.value,
    this.color,
    this.textColor,
    this.size,
    this.showBadge = true,
  });

  /// Badge value (number or text)
  final String? value;

  /// Badge background color
  final Color? color;

  /// Badge text color
  final Color? textColor;

  /// Badge size
  final double? size;

  /// Whether to show the badge
  final bool showBadge;

  @override
  Widget build(BuildContext context) {
    if (!showBadge) return const SizedBox.shrink();

    final effectiveSize = size ?? 16.0;
    final effectiveColor = color ?? AppColors.error;
    final effectiveTextColor = textColor ?? AppColors.onPrimary;

    return Container(
      constraints: BoxConstraints(
        minWidth: effectiveSize,
        minHeight: effectiveSize,
      ),
      padding: value != null 
          ? const EdgeInsets.symmetric(horizontal: 4, vertical: 2)
          : EdgeInsets.zero,
      decoration: BoxDecoration(
        color: effectiveColor,
        shape: value == null ? BoxShape.circle : BoxShape.rectangle,
        borderRadius: value != null 
            ? BorderRadius.circular(effectiveSize / 2)
            : null,
      ),
      child: value != null
          ? Center(
              child: Text(
                value!,
                style: AppTypography.labelSmall.copyWith(
                  color: effectiveTextColor,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            )
          : null,
    );
  }
}

/// Pre-built navigation items for common use cases
class AppNavigationItems {
  AppNavigationItems._();

  // Student app navigation items
  static AppBottomNavigationItem home({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.home_outlined),
        activeIcon: const Icon(Icons.home),
        label: 'Home',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  static AppBottomNavigationItem assignments({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.assignment_outlined),
        activeIcon: const Icon(Icons.assignment),
        label: 'Assignments',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  static AppBottomNavigationItem exam({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.quiz_outlined),
        activeIcon: const Icon(Icons.quiz),
        label: 'Exams',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  static AppBottomNavigationItem results({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.assessment_outlined),
        activeIcon: const Icon(Icons.assessment),
        label: 'Results',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  static AppBottomNavigationItem profile({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.person_outline),
        activeIcon: const Icon(Icons.person),
        label: 'Profile',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  // Teacher app navigation items
  static AppBottomNavigationItem teacherHome({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.dashboard_outlined),
        activeIcon: const Icon(Icons.dashboard),
        label: 'Dashboard',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  static AppBottomNavigationItem classes({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.class_outlined),
        activeIcon: const Icon(Icons.class_),
        label: 'Classes',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  static AppBottomNavigationItem lessons({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.menu_book_outlined),
        activeIcon: const Icon(Icons.menu_book),
        label: 'Lessons',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  static AppBottomNavigationItem earnings({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.account_balance_wallet_outlined),
        activeIcon: const Icon(Icons.account_balance_wallet),
        label: 'Earnings',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  static AppBottomNavigationItem teacherProfile({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.person_outline),
        activeIcon: const Icon(Icons.person),
        label: 'Profile',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  // Common navigation items
  static AppBottomNavigationItem chat({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.chat_outlined),
        activeIcon: const Icon(Icons.chat),
        label: 'Chat',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  static AppBottomNavigationItem calendar({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.calendar_today_outlined),
        activeIcon: const Icon(Icons.calendar_today),
        label: 'Calendar',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );

  static AppBottomNavigationItem notifications({String? badge}) => AppBottomNavigationItem(
        icon: const Icon(Icons.notifications_outlined),
        activeIcon: const Icon(Icons.notifications),
        label: 'Notifications',
        badge: badge != null ? NavigationBadge(value: badge) : null,
      );
} 