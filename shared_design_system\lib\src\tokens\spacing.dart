/// Spacing design tokens for eSchool apps
class AppSpacing {
  AppSpacing._();

  // Base spacing unit (4px)
  static const double unit = 4.0;

  // Spacing scale based on 4px unit
  static const double xs = unit; // 4px
  static const double sm = unit * 2; // 8px
  static const double md = unit * 3; // 12px
  static const double lg = unit * 4; // 16px
  static const double xl = unit * 5; // 20px
  static const double xxl = unit * 6; // 24px
  static const double xxxl = unit * 8; // 32px

  // Semantic spacing values
  static const double none = 0;
  static const double tiny = xs; // 4px
  static const double small = sm; // 8px
  static const double medium = lg; // 16px
  static const double large = xl; // 20px
  static const double extraLarge = xxl; // 24px
  static const double huge = xxxl; // 32px

  // Component-specific spacing
  static const double buttonPadding = lg; // 16px
  static const double cardPadding = lg; // 16px
  static const double screenPadding = xl; // 20px
  static const double sectionSpacing = xxl; // 24px
  static const double itemSpacing = md; // 12px

  // Form spacing
  static const double formFieldSpacing = lg; // 16px
  static const double formSectionSpacing = xxl; // 24px
  static const double inputPadding = lg; // 16px

  // List spacing
  static const double listItemSpacing = sm; // 8px
  static const double listSectionSpacing = lg; // 16px

  // App bar and navigation
  static const double appBarHeight = 56.0;
  static const double bottomNavHeight = 60.0;
  static const double tabBarHeight = 48.0;

  // Border radius values
  static const double radiusXs = 2.0;
  static const double radiusSm = 4.0;
  static const double radiusMd = 8.0;
  static const double radiusLg = 12.0;
  static const double radiusXl = 16.0;
  static const double radiusXxl = 20.0;
  static const double radiusRound = 999.0; // For circular elements

  // Component-specific radius
  static const double buttonRadius = radiusMd; // 8px
  static const double cardRadius = radiusLg; // 12px
  static const double inputRadius = radiusMd; // 8px
  static const double dialogRadius = radiusLg; // 12px
  static const double bottomSheetRadius = radiusXl; // 16px

  // Icon sizes
  static const double iconXs = 12.0;
  static const double iconSm = 16.0;
  static const double iconMd = 20.0;
  static const double iconLg = 24.0;
  static const double iconXl = 32.0;
  static const double iconXxl = 48.0;

  // Avatar sizes
  static const double avatarSm = 32.0;
  static const double avatarMd = 40.0;
  static const double avatarLg = 56.0;
  static const double avatarXl = 72.0;

  // Minimum touch target size (accessibility)
  static const double minTouchTarget = 44.0;

  // Screen breakpoints (for responsive design)
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;
} 