import 'package:flutter/material.dart';
import 'colors.dart';

/// Shadow design tokens for eSchool apps
class AppShadows {
  AppShadows._();

  // Base shadow configuration
  static const Color _shadowColor = AppColors.shadow;

  // Elevation level 1 - Subtle shadows for cards
  static const List<BoxShadow> elevation1 = [
    BoxShadow(
      color: _shadowColor,
      offset: Offset(0, 1),
      blurRadius: 3,
      spreadRadius: 0,
    ),
  ];

  // Elevation level 2 - Medium shadows for raised elements
  static const List<BoxShadow> elevation2 = [
    BoxShadow(
      color: _shadowColor,
      offset: Offset(0, 2),
      blurRadius: 6,
      spreadRadius: 0,
    ),
  ];

  // Elevation level 3 - More pronounced shadows for floating elements
  static const List<BoxShadow> elevation3 = [
    BoxShadow(
      color: _shadowColor,
      offset: Offset(0, 4),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  // Elevation level 4 - Strong shadows for modals and dialogs
  static const List<BoxShadow> elevation4 = [
    BoxShadow(
      color: _shadowColor,
      offset: Offset(0, 6),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];

  // Elevation level 5 - Very strong shadows for overlays
  static const List<BoxShadow> elevation5 = [
    BoxShadow(
      color: AppColors.shadowDark,
      offset: Offset(0, 8),
      blurRadius: 16,
      spreadRadius: 0,
    ),
  ];

  // Component-specific shadows
  static const List<BoxShadow> card = elevation1;
  static const List<BoxShadow> button = elevation2;
  static const List<BoxShadow> floatingActionButton = elevation3;
  static const List<BoxShadow> appBar = elevation2;
  static const List<BoxShadow> bottomSheet = elevation4;
  static const List<BoxShadow> dialog = elevation4;
  static const List<BoxShadow> menu = elevation3;
  static const List<BoxShadow> tooltip = elevation2;

  // Special shadows
  static const List<BoxShadow> inner = [
    BoxShadow(
      color: AppColors.shadowLight,
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];

  // No shadow
  static const List<BoxShadow> none = [];

  // Light shadow for subtle depth
  static const List<BoxShadow> light = [
    BoxShadow(
      color: AppColors.shadowLight,
      offset: Offset(0, 1),
      blurRadius: 2,
      spreadRadius: 0,
    ),
  ];

  // Medium shadow for standard elevation
  static const List<BoxShadow> medium = elevation2;

  // Heavy shadow for high elevation
  static const List<BoxShadow> heavy = elevation4;

  // Custom shadow builder for dynamic shadows
  static List<BoxShadow> custom({
    required double elevation,
    Color? color,
    double? opacity,
  }) {
    final shadowColor = color ?? _shadowColor;
    final effectiveColor = opacity != null 
        ? shadowColor.withOpacity(opacity)
        : shadowColor;

    return [
      BoxShadow(
        color: effectiveColor,
        offset: Offset(0, elevation * 0.5),
        blurRadius: elevation * 1.5,
        spreadRadius: 0,
      ),
    ];
  }
}

/// Material Design 3 elevation mapping
class AppElevation {
  AppElevation._();

  static const double level0 = 0.0;  // No elevation
  static const double level1 = 1.0;  // Cards, search bars
  static const double level2 = 3.0;  // FAB (resting), buttons
  static const double level3 = 6.0;  // FAB (pressed), app bars, menus
  static const double level4 = 8.0;  // Navigation drawers, modal bottom sheets
  static const double level5 = 12.0; // Dialogs, modals

  // Component-specific elevations
  static const double surface = level0;
  static const double card = level1;
  static const double button = level1;
  static const double buttonPressed = level2;
  static const double floatingActionButton = level3;
  static const double appBar = level2;
  static const double bottomNavigationBar = level2;
  static const double bottomSheet = level4;
  static const double dialog = level5;
  static const double menu = level3;
  static const double drawer = level4;
} 