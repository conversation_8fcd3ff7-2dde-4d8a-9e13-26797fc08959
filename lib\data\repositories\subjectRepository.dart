import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:eschool/data/models/studyMaterial.dart';
import 'package:eschool/data/models/subject.dart';
import 'package:eschool/data/models/subject_level.dart';
import 'package:eschool/utils/api.dart';
import 'package:eschool/domain/repositories/subject_repository_interface.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:eschool/data/services/dummy_data_service.dart';
import 'package:eschool/data/services/api_service_wrapper.dart';

class SubjectRepository implements SubjectRepositoryInterface {
  final bool useDummyData;
  final ApiServiceWrapper _apiServiceWrapper;

  // Cache key for subjects
  static const String _cacheKey = 'subjects_cache';
  // Cache expiration time (in milliseconds) - default 2 hours
  static const int _cacheExpirationTime = 7200000; // 2 hours

  SubjectRepository({this.useDummyData = false}) : _apiServiceWrapper = ApiServiceWrapper();

  @override
  Future<List<Subject>> getSubjects() async {
    if (useDummyData) {
      return getDummySubjects();
    }

    try {
      // Check if we have valid cached data first
      final cachedData = await _getCachedSubjects();
      if (cachedData != null) {
        debugPrint('SubjectRepository: Using cached subjects');
        return cachedData;
      }

      debugPrint('SubjectRepository: Fetching subjects from API');
      final result = await Api.get(
        url: Api.subjects,
        useAuthToken: true,
      );

      if (result['success'] == true) {
        final List<Subject> subjects = (result['data'] as List)
            .map((subject) => Subject.fromJson(Map.from(subject)))
            .toList();

        // Cache the result
        await _cacheSubjects(subjects);

        return subjects;
      }

      // If there's an error in the API response, return an empty list
      debugPrint('SubjectRepository: API returned error: ${result['message']}');
      return [];
    } catch (e) {
      debugPrint('SubjectRepository: Error fetching subjects: $e');
      throw ApiException(e.toString());
    }
  }

  @override
  Future<List<SubjectLevel>> getSubjectLevels(int subjectId) async {
    if (useDummyData) {
      final subject = getDummySubjects().firstWhere(
        (subject) => subject.id == subjectId,
        orElse: () => Subject(
          id: -1,
          title: 'Not Found',
          imageUrl: '',
        ),
      );
      return subject.subjectLevels;
    }

    try {
      // Check if we have valid cached subjects first
      final cachedSubjects = await _getCachedSubjects();
      if (cachedSubjects != null) {
        final subject = cachedSubjects.firstWhere(
          (subject) => subject.id == subjectId,
          orElse: () => Subject(id: -1, title: 'Not Found', imageUrl: ''),
        );

        if (subject.id != -1 && subject.subjectLevels.isNotEmpty) {
          debugPrint('SubjectRepository: Using cached subject levels for subject $subjectId');
          return subject.subjectLevels;
        }
      }

      debugPrint('SubjectRepository: Fetching subject levels from API for subject $subjectId');
      final result = await Api.get(
        url: "${Api.subjects}/$subjectId/levels",
        useAuthToken: true,
      );

      if (result['success'] == true) {
        final List<SubjectLevel> levels = (result['data'] as List? ?? [])
            .map((level) => SubjectLevel.fromJson(Map.from(level)))
            .toList();

        // Update cached subject with levels if possible
        if (cachedSubjects != null) {
          final updatedSubjects = List<Subject>.from(cachedSubjects);
          final index = updatedSubjects.indexWhere((s) => s.id == subjectId);
          if (index != -1) {
            updatedSubjects[index] = updatedSubjects[index].copyWith(subjectLevels: levels);
            await _cacheSubjects(updatedSubjects);
          }
        }

        return levels;
      }

      // If there's an error in the API response, return an empty list
      debugPrint('SubjectRepository: API returned error: ${result['message']}');
      return [];
    } catch (e) {
      debugPrint('SubjectRepository: Error fetching subject levels: $e');
      throw ApiException(e.toString());
    }
  }

  @override
  Future<Subject?> getSubjectById(int id) async {
    if (useDummyData) {
      return getDummySubjects().firstWhere(
        (subject) => subject.id == id,
        orElse: () => Subject(
          id: -1,
          title: 'Not Found',
          imageUrl: '',
        ),
      );
    }

    try {
      // Check if we have valid cached subjects first
      final cachedSubjects = await _getCachedSubjects();
      if (cachedSubjects != null) {
        final subject = cachedSubjects.firstWhere(
          (subject) => subject.id == id,
          orElse: () => Subject(id: -1, title: 'Not Found', imageUrl: ''),
        );

        if (subject.id != -1) {
          debugPrint('SubjectRepository: Using cached subject for id $id');
          return subject;
        }
      }

      debugPrint('SubjectRepository: Fetching subject from API for id $id');
      final result = await Api.get(
        url: "${Api.subjects}/$id",
        useAuthToken: true,
      );

      if (result['success'] == true) {
        final Subject subject = Subject.fromJson(result['data']);

        // Update cached subjects if possible
        if (cachedSubjects != null) {
          final updatedSubjects = List<Subject>.from(cachedSubjects);
          final index = updatedSubjects.indexWhere((s) => s.id == id);
          if (index != -1) {
            updatedSubjects[index] = subject;
          } else {
            updatedSubjects.add(subject);
          }
          await _cacheSubjects(updatedSubjects);
        }

        return subject;
      }

      // If there's an error in the API response, return null
      debugPrint('SubjectRepository: API returned error: ${result['message']}');
      return null;
    } catch (e) {
      debugPrint('SubjectRepository: Error fetching subject by id: $e');
      throw ApiException(e.toString());
    }
  }

  // Cache subjects locally
  Future<void> _cacheSubjects(List<Subject> subjects) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Serialize each Subject object to JSON
      final List<String> serializedSubjects = subjects
          .map((subject) => jsonEncode(subject.toJson()))
          .toList();

      // Save the list as a JSON string
      final Map<String, dynamic> cacheData = {
        'subjects': serializedSubjects,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await prefs.setString(_cacheKey, jsonEncode(cacheData));

      debugPrint('SubjectRepository: Cached ${subjects.length} subjects');
    } catch (e) {
      debugPrint('SubjectRepository: Error caching subjects: $e');
      // Continue without caching
    }
  }

  // Get cached subjects if available and not expired
  Future<List<Subject>?> _getCachedSubjects() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? cachedDataString = prefs.getString(_cacheKey);

      if (cachedDataString == null || cachedDataString.isEmpty) {
        return null; // No cache found
      }

      final Map<String, dynamic> cacheData = jsonDecode(cachedDataString);
      final int timestamp = cacheData['timestamp'];
      final int currentTime = DateTime.now().millisecondsSinceEpoch;

      // Check if cache is expired
      if (currentTime - timestamp > _cacheExpirationTime) {
        debugPrint('SubjectRepository: Cache expired');
        return null;
      }

      // Deserialize the subjects
      final List<dynamic> serializedSubjects = cacheData['subjects'];
      final List<Subject> subjects = serializedSubjects
          .map((subjectJson) => Subject.fromJson(jsonDecode(subjectJson)))
          .toList();

      debugPrint('SubjectRepository: Found ${subjects.length} cached subjects');
      return subjects;
    } catch (e) {
      debugPrint('SubjectRepository: Error reading cached subjects: $e');
      return null;
    }
  }

  // Invalidate cache (useful when forcing a refresh)
  Future<void> invalidateCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      debugPrint('SubjectRepository: Cache invalidated');
    } catch (e) {
      debugPrint('SubjectRepository: Error invalidating cache: $e');
    }
  }

  // Legacy methods for backward compatibility
  Future<List<StudyMaterial>> getStudyMaterialOfTopic({
    required int lessonId,
    required int topicId,
    required bool useParentApi,
    required int childId,
  }) async {
    try {
      final Map<String, dynamic> queryParameters = {
        "topic_id": topicId,
        "lesson_id": lessonId,
      };
      if (useParentApi) {
        queryParameters.addAll({"child_id": childId});
      }

      final result = await Api.get(
        url: useParentApi
            ? Api.getstudyMaterialsOfTopicParent
            : Api.getstudyMaterialsOfTopic,
        useAuthToken: true,
        queryParameters: queryParameters,
      );

      final studyMaterialJson = result['data'] as List;
      final files = (studyMaterialJson.first['file'] ?? []) as List;

      return files
          .map((file) => StudyMaterial.fromJson(Map.from(file)))
          .toList();
    } catch (e) {
      throw ApiException(e.toString());
    }
  }

  Future<void> downloadStudyMaterialFile({
    required String url,
    required String savePath,
    required CancelToken cancelToken,
    required Function updateDownloadedPercentage,
  }) async {
    try {
      await Api.download(
        cancelToken: cancelToken,
        url: url,
        savePath: savePath,
        updateDownloadedPercentage: updateDownloadedPercentage,
      );
    } catch (e) {
      throw ApiException(e.toString());
    }
  }

  // Dummy data for testing
  List<Subject> getDummySubjects() {
    return [
      Subject(
        id: 1,
        title: 'اللغة العربية',
        imageUrl: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1422&auto=format&fit=crop',
        subjectLevels: [
          SubjectLevel(id: 1, name: 'تأسيسي', subjectId: 1),
          SubjectLevel(id: 2, name: 'إعدادي', subjectId: 1),
          SubjectLevel(id: 3, name: 'ثانوي', subjectId: 1),
        ],
      ),
      Subject(
        id: 2,
        title: 'اللغة العربية للناطقين بغيرها',
        imageUrl: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1422&auto=format&fit=crop',
        subjectLevels: [
          SubjectLevel(id: 4, name: 'مبتدئ', subjectId: 2),
        ],
      ),
      Subject(
        id: 3,
        title: 'قرآن',
        imageUrl: 'https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?q=80&w=1373&auto=format&fit=crop',
        subjectLevels: [
          SubjectLevel(id: 5, name: 'حفظ', subjectId: 3),
          SubjectLevel(id: 6, name: 'تصحيح تلاوة', subjectId: 3),
        ],
      ),
      Subject(
        id: 4,
        title: 'لغة إنجليزية',
        imageUrl: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=1470&auto=format&fit=crop',
        subjectLevels: [
          SubjectLevel(id: 7, name: 'تأسيسي', subjectId: 4),
          SubjectLevel(id: 8, name: 'إعدادي', subjectId: 4),
          SubjectLevel(id: 9, name: 'ثانوي', subjectId: 4),
        ],
      ),
      Subject(
        id: 5,
        title: 'دورات تخصصية',
        imageUrl: 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?q=80&w=1470&auto=format&fit=crop',
        subjectLevels: [
          SubjectLevel(id: 10, name: 'specialized', subjectId: 5),
          SubjectLevel(id: 11, name: 'professional', subjectId: 5),
        ],
      ),
    ];
  }

  /// Get student subjects with dummy data fallback
  /// Falls back to dummy data when API is unavailable or in debug mode
  Future<Map<String, dynamic>> fetchStudentSubjects({
    bool useAuthToken = true,
    int? classSectionId,
    String? token,
  }) async {
    Map<String, dynamic> queryParameters = {};
    if (classSectionId != null) {
      queryParameters.addAll({'class_section_id': classSectionId.toString()});
    }

    return await _apiServiceWrapper.fetchSubjects(
      queryParams: queryParameters.isNotEmpty ? queryParameters : null,
      token: token,
    );
  }

  /// Get class subjects (specific to a class) with dummy data fallback
  Future<Map<String, dynamic>> fetchClassSubjects({
    required int classSectionId,
    bool useAuthToken = true,
    String? token,
  }) async {
    return await _apiServiceWrapper.fetchSubjects(
      queryParams: {
        'class_section_id': classSectionId.toString(),
      },
      token: token,
    );
  }

  /// Get subject lessons with dummy data fallback
  Future<Map<String, dynamic>> fetchSubjectLessons({
    required int subjectId,
    bool useAuthToken = true,
    String? token,
  }) async {
    return await _apiServiceWrapper.fetchLessons(
      subjectId: subjectId,
      token: token,
    );
  }

  /// Get study materials of a specific topic/lesson with dummy data fallback
  Future<Map<String, dynamic>> fetchStudyMaterials({
    required int lessonId,
    bool useAuthToken = true,
    String? token,
  }) async {
    return await _apiServiceWrapper.fetchLessonById(
      lessonId: lessonId,
      token: token,
    );
  }
}
