import 'dart:convert';
import 'package:eschool/data/models/package.dart';
import 'package:eschool/data/services/api_service_wrapper.dart';
import 'package:eschool/utils/api.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PackageRepository {
  final ApiServiceWrapper _apiServiceWrapper;

  // Cache key for packages
  static const String _cacheKey = 'packages_cache';
  // Cache expiration time (in milliseconds) - default 3 hours
  static const int _cacheExpirationTime = 10800000; // 3 hours

  PackageRepository() : _apiServiceWrapper = ApiServiceWrapper();

  Future<List<Package>> fetchPackages({int? subject, dynamic level, String? token}) async {
    try {
      // Check if we have valid cached data first
      final cachedData = await _getCachedPackages();
      if (cachedData != null) {
        debugPrint('PackageRepository: Using cached packages');
        // Apply filters to cached data
        return _filterPackages(cachedData, subject, level);
      }

      // No valid cache, fetch from API using ApiServiceWrapper
      debugPrint('PackageRepository: Fetching packages from API');
      final Map<String, dynamic> queryParameters = {};

      if (subject != null) {
        queryParameters['subject'] = subject;
      }

      if (level != null) {
        queryParameters['level'] = level;
      }

      final result = await _apiServiceWrapper.fetchPackages(
        queryParams: queryParameters.isNotEmpty ? queryParameters : null,
        token: token,
      );

      if (result['success'] == true) {
        final List<Package> packages = (result['data'] as List)
            .map((package) => Package.fromJson(Map.from(package)))
            .toList();

        // Cache the results
        await _cachePackages(packages);

        // Return filtered packages
        return _filterPackages(packages, subject, level);
      }

      // Fallback to dummy data if API fails
      return getDummyPackages(subject: subject, level: level?.toString());
    } catch (e) {
      debugPrint('PackageRepository: Error fetching packages: $e');
      // On error, try to use cached data if available, or fallback to dummy data
      final cachedData = await _getCachedPackages();
      if (cachedData != null) {
        debugPrint('PackageRepository: Using cached packages after error');
        return _filterPackages(cachedData, subject, level);
      }

      return getDummyPackages(subject: subject, level: level?.toString());
    }
  }

  // Filter packages based on subject and level
  List<Package> _filterPackages(List<Package> packages, int? subject, dynamic level) {
    if (subject != null) {
      // Filter by subject
      packages = packages.where((package) => package.hasSubject(subject)).toList();
    }

    if (level != null) {
      // Filter by level
      String levelStr = level.toString();
      packages = packages.where((package) => package.hasLevel(levelStr)).toList();
    }

    return packages;
  }

  // Cache packages data
  Future<void> _cachePackages(List<Package> packages) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final Map<String, dynamic> cacheData = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'packages': packages.map((package) => package.toJson()).toList(),
      };

      await prefs.setString(_cacheKey, jsonEncode(cacheData));
      debugPrint('PackageRepository: Packages cached successfully');
    } catch (e) {
      debugPrint('PackageRepository: Error caching packages: $e');
    }
  }

  // Get cached packages data if it's still valid
  Future<List<Package>?> _getCachedPackages() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? cachedDataString = prefs.getString(_cacheKey);

      if (cachedDataString == null || cachedDataString.isEmpty) {
        return null;
      }

      final Map<String, dynamic> cachedData = jsonDecode(cachedDataString);
      final int timestamp = cachedData['timestamp'] ?? 0;
      final int currentTime = DateTime.now().millisecondsSinceEpoch;

      // Check if cache is still valid
      if (currentTime - timestamp > _cacheExpirationTime) {
        debugPrint('PackageRepository: Cache expired');
        return null;
      }

      // Parse cached packages
      final List<dynamic> packagesList = cachedData['packages'] ?? [];
      final List<Package> packages = packagesList
          .map((packageJson) => Package.fromJson(Map<String, dynamic>.from(packageJson)))
          .toList();

      return packages;
    } catch (e) {
      debugPrint('PackageRepository: Error reading cache: $e');
      return null;
    }
  }

  // For testing with dummy data
  List<Package> getDummyPackages({int? subject, String? level}) {
    final List<Package> allPackages = [
      Package(
        id: "3",
        title: "باقة احترافية",
        description: "أربع حصص في الأسبوع",
        price: 160.0,
        numberOfLectures: 4,
        imageUrl: "assets/images/packages/premium.png",
        isPopular: false,
        features: ["أربع حصص", "مدة الحصة 45 دقيقة", "دعم فني", "مواد تعليمية إضافية", "جلسات خاصة"],
        subjectLevels: {
          1: ["تأسيسي", "إعدادي", "ثانوي"], // اللغة العربية
          3: ["حفظ", "تصحيح تلاوة"], // قرآن - now id 3
          4: ["تأسيسي", "إعدادي", "ثانوي"], // لغة إنجليزية - now id 4
        },
      ),
      Package(
        id: "4",
        title: "باقة ال8 دروس الاحترافية",
        description: "ثمان حصص في الأسبوع",
        price: 320.0,
        numberOfLectures: 8,
        imageUrl: "assets/images/packages/premium-plus.png",
        isPopular: true,
        features: ["ثمان حصص", "مدة الحصة 45 دقيقة", "دعم فني", "مواد تعليمية إضافية", "جلسات خاصة", "مذكرات تعليمية"],
        subjectLevels: {
          1: ["تأسيسي", "إعدادي", "ثانوي"], // اللغة العربية
          3: ["حفظ", "تصحيح تلاوة"], // قرآن - now id 3
          4: ["تأسيسي", "إعدادي", "ثانوي"], // لغة إنجليزية - now id 4
          5: ["specialized", "professional"], // دورات تخصصية - now id 5
          2: ["مبتدئ"], // اللغة العربية للناطقين بغيرها - now id 2
        },
      ),
      Package(
        id: "5",
        title: "باقة ال12 درس شاملة",
        description: "اثنى عشر حصة في الأسبوع",
        price: 480.0,
        numberOfLectures: 12,
        imageUrl: "assets/images/packages/premium-extra.png",
        isPopular: false,
        features: ["اثنى عشر حصة", "مدة الحصة 45 دقيقة", "دعم فني", "مواد تعليمية إضافية", "جلسات خاصة", "مذكرات تعليمية", "اختبارات أسبوعية"],
        subjectLevels: {
          1: ["تأسيسي", "إعدادي", "ثانوي"], // اللغة العربية
          3: ["حفظ", "تصحيح تلاوة"], // قرآن - now id 3
          4: ["تأسيسي", "إعدادي", "ثانوي"], // لغة إنجليزية - now id 4
          5: ["specialized", "professional"], // دورات تخصصية - now id 5
          2: ["مبتدئ"], // اللغة العربية للناطقين بغيرها - now id 2
        },
      ),
      Package(
        id: "6",
        title: "حزمة الفصل الدراسي",
        description: "حزمة حصص لفصل دراسي كامل",
        price: 2400.0,
        numberOfLectures: 60,
        imageUrl: "assets/images/packages/semester.png",
        isPopular: false,
        features: ["ستون حصة", "مدة الحصة 45 دقيقة", "دعم فني", "مواد تعليمية إضافية", "جلسات خاصة", "مذكرات تعليمية", "اختبارات أسبوعية", "شهادة إتمام"],
        subjectLevels: {
          1: ["تأسيسي", "إعدادي", "ثانوي"], // اللغة العربية
          3: ["حفظ", "تصحيح تلاوة"], // قرآن - now id 3
          4: ["تأسيسي", "إعدادي", "ثانوي"], // لغة إنجليزية - now id 4
          5: ["specialized", "professional"], // دورات تخصصية - now id 5
          2: ["مبتدئ"], // اللغة العربية للناطقين بغيرها - now id 2
        },
      ),
      Package(
        id: "7",
        title: "حزمة الإجازة الصيفية",
        description: "حزمة حصص مكثفة للإجازة الصيفية",
        price: 1200.0,
        numberOfLectures: 30,
        imageUrl: "assets/images/packages/summer.png",
        isPopular: false,
        features: ["ثلاثون حصة", "مدة الحصة 45 دقيقة", "دعم فني", "مواد تعليمية إضافية", "جلسات خاصة", "مذكرات تعليمية", "أنشطة صيفية"],
        subjectLevels: {
          1: ["تأسيسي", "إعدادي", "ثانوي"], // اللغة العربية
          3: ["حفظ", "تصحيح تلاوة"], // قرآن - now id 3
          4: ["تأسيسي", "إعدادي", "ثانوي"], // لغة إنجليزية - now id 4
          5: ["specialized", "professional"], // دورات تخصصية - now id 5
        },
      ),
      // Package with an empty subject (has a subject ID but no levels)
      Package(
        id: "8",
        title: "باقة خاصة",
        description: "باقة خاصة بالمواضيع بدون مستويات",
        price: 150.0,
        numberOfLectures: 5,
        imageUrl: "assets/images/packages/special.png",
        isPopular: false,
        features: ["خمس حصص", "مدة الحصة 45 دقيقة", "دعم فني"],
        subjectLevels: {
          6: [], // Subject with no levels
        },
      ),
    ];

    return _filterPackages(allPackages, subject, level);
  }
}
