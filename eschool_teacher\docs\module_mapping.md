# Module Mapping: Student App to Teacher App

This document outlines which modules and components from the student app can be adapted for the teacher app implementation according to the PRD requirements.

## UI Components

| Component | Student App Location | Teacher App Location | Adaptation Needed |
|-----------|---------------------|----------------------|-------------------|
| Chat Interface | `lib/ui/screens/chat/` | `eschool_teacher/lib/ui/screens/chat/` | Medium - Update for teacher context |
| User Profile | `lib/ui/screens/studentProfileScreen.dart` | `eschool_teacher/lib/ui/screens/profile/` | Medium - Add earnings & IBAN |
| Assignment List | `lib/ui/screens/assignment/` | `eschool_teacher/lib/ui/screens/assignment/` | High - Change to creation view |
| Marketplace UI | `lib/ui/screens/marketplace/` | N/A - Teachers don't purchase | Not applicable |
| Auth Screens | `lib/ui/screens/auth/` | `eschool_teacher/lib/ui/screens/login/` | Medium - Add 2FA |

## Models

| Model | Student App Location | Needed for Teacher App | Adaptation Level |
|-------|---------------------|------------------------|------------------|
| Chat Models | `lib/data/models/chat/` | Yes | Low - Minor adjustments |
| User Model | `lib/data/models/student.dart` | Partial | Medium - Create teacher version |
| Assignment Models | `lib/data/models/assignment.dart` | Yes | Medium - Add grading fields |
| Lesson/Class Models | `lib/data/models/lesson.dart` | Yes | High - Add recurring schedule |
| Payment Models | `lib/data/models/payment*.dart` | Partial | High - Convert to earnings |

## Repositories

| Repository | Student App Location | Teacher App Equivalent | Reusability |
|------------|---------------------|------------------------|-------------|
| Chat Repository | `lib/data/repositories/chatRepository.dart` | Exists | High |
| Auth Repository | `lib/data/repositories/authRepository.dart` | Exists | Medium - Add 2FA |
| Assignment Repository | `lib/data/repositories/assignmentRepository.dart` | Exists | Medium - Modify |
| Lesson Repository | `lib/data/repositories/lessonRepository.dart` | Enhance TeacherRepository | Low |
| Payment Repository | `lib/data/repositories/paymentRepository.dart` | Need new EarningsRepository | Low |

## Cubits/State Management

| Cubit | Student App Location | Teacher App Equivalent | Adaptation |
|-------|---------------------|------------------------|------------|
| Chat Cubits | `lib/cubits/chat/` | Exists | Medium |
| Auth Cubits | `lib/cubits/authCubit.dart` | Exists | Medium - Add 2FA |
| Assignment Cubits | `lib/cubits/assignment*.dart` | Exists | Medium |
| Lesson Cubits | `lib/cubits/lesson*.dart` | Partial | High |
| Payment Cubits | `lib/cubits/payment*.dart` | Need new | High |

## New Models to Create

### 1. Earnings Model
```dart
class Earnings {
  final double totalEarnings;
  final double availableBalance;
  final double pendingAmount;
  final List<Transaction> transactions;
  
  // Constructor and JSON serialization
}
```

### 2. Transaction Model
```dart
class Transaction {
  final String id;
  final double amount;
  final String type; // "lesson_payment", "withdrawal", etc.
  final DateTime date;
  final String status; // "completed", "pending", "cancelled"
  final String? description;
  
  // Constructor and JSON serialization
}
```

### 3. WithdrawalRequest Model
```dart
class WithdrawalRequest {
  final String id;
  final double amount;
  final DateTime requestDate;
  final String status; // "pending", "approved", "rejected"
  final String? rejectReason;
  final String? iban;
  
  // Constructor and JSON serialization
}
```

### 4. Enhanced Lesson Model
```dart
class EnhancedLesson {
  final String id;
  final String title;
  final String type; // "individual", "group"
  final DateTime startTime;
  final DateTime endTime;
  final List<String> studentIds;
  final String? zoomLink;
  final int rescheduleCount; // Track reschedules (max 2)
  final RecurringPattern? recurringPattern; // Null if not recurring
  
  // Constructor and JSON serialization
}

class RecurringPattern {
  final String frequency; // "weekly", "biweekly", etc.
  final int occurrences; // Total number of occurrences
  final List<DateTime>? exceptionDates; // Dates to skip
  
  // Constructor and JSON serialization
}
```

### 5. ZoomMeeting Model
```dart
class ZoomMeeting {
  final String id;
  final String meetingId;
  final String password;
  final String joinUrl;
  final DateTime startTime;
  final DateTime endTime;
  final String lessonId;
  
  // Constructor and JSON serialization
}
```

## UI Adaptations

1. **Home Screen**
   - Convert student dashboard to teacher view with:
     - Upcoming lessons/classes
     - Pending assignments to review
     - Earnings summary widget
     - Pending withdrawal requests

2. **Lesson Management**
   - Enhance existing class management to add:
     - Individual/group selection
     - Recurring schedule options
     - Exception date selection
     - Zoom meeting integration

3. **Earnings Dashboard (New)**
   - Create new screen with:
     - Earnings summary cards
     - Transaction history list
     - Withdrawal request form
     - IBAN management

4. **Assignment Review**
   - Enhance existing screens with:
     - Batch assignment creation
     - Multiple student selection
     - Better grading interface
     - File management improvements 