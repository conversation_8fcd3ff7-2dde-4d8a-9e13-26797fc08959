import 'package:eschool_teacher/app/routes.dart';
import 'package:eschool_teacher/cubits/assignmentCubit.dart';
import 'package:eschool_teacher/cubits/deleteassignmentcubit.dart';
import 'package:eschool_teacher/data/models/classSectionDetails.dart';
import 'package:eschool_teacher/data/models/subject.dart';
import 'package:eschool_teacher/data/repositories/assignmentRepository.dart';
import 'package:eschool_teacher/ui/styles/designSystem.dart';
import 'package:eschool_teacher/ui/widgets/confirmDeleteDialog.dart';
import 'package:eschool_teacher/ui/widgets/customShimmerContainer.dart';
import 'package:eschool_teacher/ui/widgets/deleteButton.dart';
import 'package:eschool_teacher/ui/widgets/editButton.dart';
import 'package:eschool_teacher/ui/widgets/errorContainer.dart';
import 'package:eschool_teacher/ui/widgets/noDataContainer.dart';
import 'package:eschool_teacher/ui/widgets/shimmerLoadingContainer.dart';
import 'package:eschool_teacher/utils/animationConfiguration.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
// ignore: depend_on_referenced_packages
import 'package:eschool_teacher/data/models/assignment.dart';
import 'package:eschool_teacher/ui/screens/assignments/widgets/assignmentDetailsBottomsheetContainer.dart';

import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AssignmentsContainer extends StatelessWidget {
  final ClassSectionDetails classSectionDetails;
  final Subject subject;

  const AssignmentsContainer({
    super.key,
    required this.classSectionDetails,
    required this.subject,
  });

  void showAssignmentBottomSheet({
    required BuildContext context,
    required Assignment assignment,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: DesignSystem.surfaceColor,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(DesignSystem.radius2xl),
            topRight: Radius.circular(DesignSystem.radius2xl),
          ),
        ),
        child: AssignmentDetailsBottomsheetContainer(assignment: assignment),
      ),
    );
  }

  Widget _buildAssignmentStatusChip(Assignment assignment) {
    final now = DateTime.now();
    final dueDate = assignment.dueDate ?? DateTime.now();
    final isOverdue = dueDate.isBefore(now);
    final isDueSoon = dueDate.difference(now).inDays <= 1 && !isOverdue;
    
    Color chipColor;
    String statusText;
    IconData statusIcon;
    
    if (isOverdue) {
      chipColor = DesignSystem.errorColor;
      statusText = "Overdue";
      statusIcon = Icons.warning;
    } else if (isDueSoon) {
      chipColor = DesignSystem.warningColor;
      statusText = "Due Soon";
      statusIcon = Icons.schedule;
    } else {
      chipColor = DesignSystem.successColor;
      statusText = "Active";
      statusIcon = Icons.check_circle;
    }
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacingSm,
        vertical: DesignSystem.spacing2xs,
      ),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: DesignSystem.borderRadiusSm,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            statusIcon,
            size: DesignSystem.iconSm,
            color: chipColor,
          ),
          SizedBox(width: DesignSystem.spacing2xs),
          Text(
            statusText,
            style: DesignSystem.labelSmall.copyWith(
              color: chipColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssignmentCard(Assignment assignment) {
    return BlocProvider<DeleteAssignmentCubit>(
      create: (context) => DeleteAssignmentCubit(AssignmentRepository()),
      child: Builder(
        builder: (context) {
          return BlocConsumer<DeleteAssignmentCubit, DeleteAssignmentState>(
            listener: (context, state) {
              if (state is DeleteAssignmentFetchSuccess) {
                context.read<AssignmentCubit>().deleteAssignment(int.tryParse(assignment.id) ?? 0);
                UiUtils.showBottomToastOverlay(
                  context: context,
                  errorMessage: "Assignment deleted successfully",
                  backgroundColor: DesignSystem.successColor,
                );
              } else if (state is DeleteAssignmentFetchFailure) {
                UiUtils.showBottomToastOverlay(
                  context: context,
                  errorMessage: UiUtils.getTranslatedLabel(context, unableToDeleteAssignmentKey),
                  backgroundColor: DesignSystem.errorColor,
                );
              }
            },
            builder: (context, state) {
              final isDeleting = state is DeleteAssignmentFetchInProgress;
              
              return Container(
                margin: EdgeInsets.symmetric(
                  horizontal: DesignSystem.spacing2xl,
                  vertical: DesignSystem.spacingSm,
                ),
                decoration: DesignSystem.cardDecoration.copyWith(
                  boxShadow: isDeleting ? null : DesignSystem.shadowMd,
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: isDeleting ? null : () {
                      showAssignmentBottomSheet(
                        context: context,
                        assignment: assignment,
                      );
                    },
                    borderRadius: DesignSystem.borderRadiusLg,
                    child: AnimatedOpacity(
                      duration: DesignSystem.animationFast,
                      opacity: isDeleting ? 0.5 : 1.0,
                      child: Padding(
                        padding: DesignSystem.paddingLg,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        assignment.name ?? "",
                                        style: DesignSystem.titleMedium.copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      SizedBox(height: DesignSystem.spacingXs),
                                      _buildAssignmentStatusChip(assignment),
                                    ],
                                  ),
                                ),
                                SizedBox(width: DesignSystem.spacingMd),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: DesignSystem.primaryColor.withValues(alpha: 0.1),
                                        borderRadius: DesignSystem.borderRadiusSm,
                                      ),
                                      child: EditButton(
                                        onTap: isDeleting ? () {} : () {
                                          Navigator.of(context).pushNamed<bool?>(
                                            Routes.addAssignment,
                                            arguments: {
                                              "editAssignment": true,
                                              "assignment": assignment,
                                            },
                                          ).then((value) {
                                            if (value != null && value) {
                                              context.read<AssignmentCubit>().fetchAssignment(
                                                classSectionId: assignment.classSectionId,
                                                subjectId: assignment.subjectId,
                                              );
                                            }
                                          });
                                        },
                                      ),
                                    ),
                                    SizedBox(width: DesignSystem.spacingSm),
                                    Container(
                                      decoration: BoxDecoration(
                                        color: DesignSystem.errorColor.withValues(alpha: 0.1),
                                        borderRadius: DesignSystem.borderRadiusSm,
                                      ),
                                      child: DeleteButton(
                                        onTap: isDeleting ? () {} : () {
                                          showDialog<bool>(
                                            context: context,
                                            builder: (_) => const ConfirmDeleteDialog(),
                                          ).then((value) {
                                            if (value != null && value) {
                                              context.read<DeleteAssignmentCubit>().deleteAssignment(
                                                assignmentId: assignment.id,
                                              );
                                            }
                                          });
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            SizedBox(height: DesignSystem.spacingMd),
                            Container(
                              padding: DesignSystem.paddingMd,
                              decoration: BoxDecoration(
                                color: DesignSystem.backgroundColor,
                                borderRadius: DesignSystem.borderRadiusSm,
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.schedule,
                                    size: DesignSystem.iconSm,
                                    color: DesignSystem.textSecondary,
                                  ),
                                  SizedBox(width: DesignSystem.spacingSm),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          UiUtils.getTranslatedLabel(context, dueDateKey),
                                          style: DesignSystem.labelSmall.copyWith(
                                            color: DesignSystem.textSecondary,
                                          ),
                                        ),
                                        Text(
                                          UiUtils.formatDateAndTime(assignment.dueDate),
                                          style: DesignSystem.bodySmall.copyWith(
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (assignment.instructions.isNotEmpty) ...[
                                    Container(
                                      padding: EdgeInsets.all(DesignSystem.spacingXs),
                                      decoration: BoxDecoration(
                                        color: DesignSystem.infoColor.withValues(alpha: 0.1),
                                        borderRadius: DesignSystem.borderRadiusSm,
                                      ),
                                      child: Icon(
                                        Icons.description,
                                        size: DesignSystem.iconSm,
                                        color: DesignSystem.infoColor,
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                            if (isDeleting) ...[
                              SizedBox(height: DesignSystem.spacingMd),
                              Row(
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        DesignSystem.errorColor,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: DesignSystem.spacingSm),
                                  Text(
                                    "Deleting assignment...",
                                    style: DesignSystem.bodySmall.copyWith(
                                      color: DesignSystem.errorColor,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildShimmerLoadingCard() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingSm,
      ),
      padding: DesignSystem.paddingLg,
      decoration: DesignSystem.cardDecoration,
      child: ShimmerLoadingContainer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomShimmerContainer(
                        height: 20,
                        width: double.infinity,
                        borderRadius: DesignSystem.radiusSm,
                      ),
                      SizedBox(height: DesignSystem.spacingSm),
                      CustomShimmerContainer(
                        height: 16,
                        width: 100,
                        borderRadius: DesignSystem.radiusSm,
                      ),
                    ],
                  ),
                ),
                SizedBox(width: DesignSystem.spacingMd),
                Row(
                  children: [
                    CustomShimmerContainer(
                      height: 32,
                      width: 32,
                      borderRadius: DesignSystem.radiusSm,
                    ),
                    SizedBox(width: DesignSystem.spacingSm),
                    CustomShimmerContainer(
                      height: 32,
                      width: 32,
                      borderRadius: DesignSystem.radiusSm,
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: DesignSystem.spacingMd),
            CustomShimmerContainer(
              height: 60,
              width: double.infinity,
              borderRadius: DesignSystem.radiusSm,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AssignmentCubit, AssignmentState>(
      bloc: context.read<AssignmentCubit>(),
      builder: (context, state) {
        if (state is AssignmentsFetchSuccess) {
          return state.assignment.isEmpty
              ? Container(
                  margin: EdgeInsets.all(DesignSystem.spacing2xl),
                  child: const NoDataContainer(titleKey: noAssignmentsKey),
                )
              : Column(
                  children: state.assignment
                      .map(
                        (assignment) => Animate(
                          effects: customItemFadeAppearanceEffects(),
                          child: _buildAssignmentCard(assignment),
                        ),
                      )
                      .toList(),
                );
        }
        
        if (state is AssignmentFetchFailure) {
          return Container(
            margin: EdgeInsets.all(DesignSystem.spacing2xl),
            child: ErrorContainer(
              errorMessageCode: state.errorMessage,
              onTapRetry: () {
                context.read<AssignmentCubit>().fetchAssignment(
                  classSectionId: classSectionDetails.id,
                  subjectId: subject.id,
                );
              },
            ),
          );
        }
        
        return Column(
          children: List.generate(
            5,
            (index) => _buildShimmerLoadingCard(),
          ),
        );
      },
    );
  }
}
