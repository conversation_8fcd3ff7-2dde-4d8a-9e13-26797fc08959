import 'package:eschool_teacher/app/routes.dart';
import 'package:eschool_teacher/cubits/createAssignmentCubit.dart';
import 'package:eschool_teacher/cubits/editassignment.dart';
import 'package:eschool_teacher/cubits/dashboardCubit.dart';
import 'package:eschool_teacher/cubits/subjectsOfClassSectionCubit.dart';
import 'package:eschool_teacher/data/models/assignment.dart';
import 'package:eschool_teacher/data/models/studyMaterial.dart';
import 'package:eschool_teacher/data/repositories/assignmentRepository.dart';
import 'package:eschool_teacher/data/repositories/teacherRepository.dart';
import 'package:eschool_teacher/ui/styles/designSystem.dart';
import 'package:eschool_teacher/ui/widgets/customDropDownMenu.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'package:permission_handler/permission_handler.dart';

class CreateAssignmentScreen extends StatefulWidget {
  final bool isEditing;
  final Assignment? assignment;

  const CreateAssignmentScreen({
    super.key,
    this.isEditing = false,
    this.assignment,
  });

  static Route<bool?> route(RouteSettings routeSettings) {
    final arguments = routeSettings.arguments as Map<String, dynamic>?;
    return CupertinoPageRoute(
      builder: (context) {
        return MultiBlocProvider(
          providers: [
            BlocProvider<SubjectsOfClassSectionCubit>(
              create: (_) => SubjectsOfClassSectionCubit(TeacherRepository()),
            ),
            BlocProvider<CreateAssignmentCubit>(
              create: (_) => CreateAssignmentCubit(AssignmentRepository()),
            ),
            BlocProvider<EditAssignmentCubit>(
              create: (context) => EditAssignmentCubit(AssignmentRepository()),
            ),
          ],
          child: CreateAssignmentScreen(
            isEditing: arguments?["editAssignment"] ?? false,
            assignment: arguments?["assignment"],
          ),
        );
      },
    );
  }

  @override
  State<CreateAssignmentScreen> createState() => _CreateAssignmentScreenState();
}

class _CreateAssignmentScreenState extends State<CreateAssignmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  
  // Controllers
  late final TextEditingController _nameController = TextEditingController(
    text: widget.isEditing ? widget.assignment!.name : null,
  );
  late final TextEditingController _instructionsController = TextEditingController(
    text: widget.isEditing ? widget.assignment!.instructions : null,
  );
  late final TextEditingController _pointsController = TextEditingController(
    text: widget.isEditing ? widget.assignment!.points.toString() : null,
  );
  late final TextEditingController _resubmissionDaysController = TextEditingController(
    text: widget.isEditing ? widget.assignment!.extraDaysForResubmission.toString() : "0",
  );

  // State variables
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  bool _allowResubmission = false;
  List<PlatformFile> _uploadedFiles = [];
  List<StudyMaterial> _existingAttachments = [];
  
  // Dropdown selections
  CustomDropDownItem? _selectedClassSection;
  CustomDropDownItem? _selectedSubject;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    if (widget.isEditing && widget.assignment != null) {
      final assignment = widget.assignment!;
      _selectedDate = assignment.dueDate ?? DateTime.now();
      _selectedTime = TimeOfDay.fromDateTime(assignment.dueDate ?? DateTime.now());
      _allowResubmission = (assignment.resubmission ?? 0) != 0;
      _existingAttachments = assignment.studyMaterial ?? [];
      
      // Initialize class section
      final classSectionDetails = context.read<DashboardCubit>()
          .getClassSectionDetailsById(assignment.classSectionId ?? 0);
      _selectedClassSection = CustomDropDownItem(
        index: 0,
        title: classSectionDetails.getClassSectionNameWithMedium(),
      );
    } else {
      // Initialize with first available class section
      final classSections = context.read<DashboardCubit>().getClassSectionName();
      if (classSections.isNotEmpty) {
        _selectedClassSection = CustomDropDownItem(
          index: 0,
          title: classSections.first,
        );
        _fetchSubjects();
      }
    }
  }

  void _fetchSubjects() {
    if (_selectedClassSection != null) {
      final classSectionDetails = context.read<DashboardCubit>()
          .getClassSectionDetails(index: _selectedClassSection!.index);
      context.read<SubjectsOfClassSectionCubit>()
          .fetchSubjects(classSectionDetails.id);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _instructionsController.dispose();
    _pointsController.dispose();
    _resubmissionDaysController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Widget _buildAppBar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingLg,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DesignSystem.primaryColor,
            DesignSystem.primaryLight,
          ],
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: DesignSystem.textOnPrimary,
                size: DesignSystem.iconLg,
              ),
            ),
            SizedBox(width: DesignSystem.spacingMd),
            Expanded(
              child: Text(
                widget.isEditing 
                    ? UiUtils.getTranslatedLabel(context, editAssignmentKey)
                    : UiUtils.getTranslatedLabel(context, createAssignmentKey),
                style: DesignSystem.headingMedium.copyWith(
                  color: DesignSystem.textOnPrimary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingMd,
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(DesignSystem.spacingSm),
            decoration: BoxDecoration(
              color: DesignSystem.primaryColor.withValues(alpha: 0.1),
              borderRadius: DesignSystem.borderRadiusSm,
            ),
            child: Icon(
              icon,
              size: DesignSystem.iconMd,
              color: DesignSystem.primaryColor,
            ),
          ),
          SizedBox(width: DesignSystem.spacingMd),
          Text(
            title,
            style: DesignSystem.titleLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    Widget? suffix,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingSm,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: DesignSystem.labelMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: DesignSystem.spacingSm),
          TextFormField(
            controller: controller,
            maxLines: maxLines,
            keyboardType: keyboardType,
            inputFormatters: inputFormatters,
            validator: validator,
            style: DesignSystem.bodyMedium,
            decoration: DesignSystem.inputDecoration(
              hintText: hint,
              suffixIcon: suffix,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateTimeSelector() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingSm,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            UiUtils.getTranslatedLabel(context, dueDateKey),
            style: DesignSystem.labelMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: DesignSystem.spacingSm),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: _selectDate,
                  child: Container(
                    padding: DesignSystem.paddingLg,
                    decoration: BoxDecoration(
                      border: Border.all(color: DesignSystem.textTertiary),
                      borderRadius: DesignSystem.borderRadiusLg,
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: DesignSystem.iconMd,
                          color: DesignSystem.primaryColor,
                        ),
                        SizedBox(width: DesignSystem.spacingMd),
                        Expanded(
                          child: Text(
                            _selectedDate != null
                                ? DateFormat('MMM dd, yyyy').format(_selectedDate!)
                                : "Select Date",
                            style: DesignSystem.bodyMedium.copyWith(
                              color: _selectedDate != null 
                                  ? DesignSystem.textPrimary 
                                  : DesignSystem.textTertiary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(width: DesignSystem.spacingMd),
              Expanded(
                child: GestureDetector(
                  onTap: _selectTime,
                  child: Container(
                    padding: DesignSystem.paddingLg,
                    decoration: BoxDecoration(
                      border: Border.all(color: DesignSystem.textTertiary),
                      borderRadius: DesignSystem.borderRadiusLg,
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: DesignSystem.iconMd,
                          color: DesignSystem.primaryColor,
                        ),
                        SizedBox(width: DesignSystem.spacingMd),
                        Expanded(
                          child: Text(
                            _selectedTime != null
                                ? _selectedTime!.format(context)
                                : "Select Time",
                            style: DesignSystem.bodyMedium.copyWith(
                              color: _selectedTime != null 
                                  ? DesignSystem.textPrimary 
                                  : DesignSystem.textTertiary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResubmissionToggle() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingSm,
      ),
      padding: DesignSystem.paddingLg,
      decoration: DesignSystem.cardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Allow Resubmission",
                      style: DesignSystem.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: DesignSystem.spacingXs),
                    Text(
                      "Students can resubmit if their assignment is rejected",
                      style: DesignSystem.bodySmall.copyWith(
                        color: DesignSystem.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: _allowResubmission,
                onChanged: (value) {
                  setState(() {
                    _allowResubmission = value;
                  });
                },
                activeColor: DesignSystem.primaryColor,
              ),
            ],
          ),
          if (_allowResubmission) ...[
            SizedBox(height: DesignSystem.spacingMd),
            TextFormField(
              controller: _resubmissionDaysController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              style: DesignSystem.bodyMedium,
              decoration: DesignSystem.inputDecoration(
                hintText: "Extra days for resubmission",
                prefixIcon: const Icon(Icons.schedule),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFileUploadSection() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingSm,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Attachments",
            style: DesignSystem.labelMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: DesignSystem.spacingSm),
          GestureDetector(
            onTap: _addFiles,
            child: Container(
              width: double.infinity,
              padding: DesignSystem.paddingXl,
              decoration: BoxDecoration(
                border: Border.all(
                  color: DesignSystem.primaryColor,
                  style: BorderStyle.solid,
                  width: 2,
                ),
                borderRadius: DesignSystem.borderRadiusLg,
                color: DesignSystem.primaryColor.withValues(alpha: 0.05),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.cloud_upload_outlined,
                    size: DesignSystem.icon2xl,
                    color: DesignSystem.primaryColor,
                  ),
                  SizedBox(height: DesignSystem.spacingMd),
                  Text(
                    "Tap to upload files",
                    style: DesignSystem.titleMedium.copyWith(
                      color: DesignSystem.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: DesignSystem.spacingXs),
                  Text(
                    "PDF, DOC, Images supported",
                    style: DesignSystem.bodySmall.copyWith(
                      color: DesignSystem.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (_uploadedFiles.isNotEmpty || _existingAttachments.isNotEmpty) ...[
            SizedBox(height: DesignSystem.spacingMd),
            ..._uploadedFiles.map((file) => _buildFileItem(file.name, true)),
            ..._existingAttachments.map((attachment) => _buildFileItem(attachment.fileName, false)),
          ],
        ],
      ),
    );
  }

  Widget _buildFileItem(String fileName, bool isNew) {
    return Container(
      margin: EdgeInsets.only(bottom: DesignSystem.spacingSm),
      padding: DesignSystem.paddingMd,
      decoration: BoxDecoration(
        color: isNew 
            ? DesignSystem.successColor.withValues(alpha: 0.1)
            : DesignSystem.backgroundColor,
        borderRadius: DesignSystem.borderRadiusSm,
        border: Border.all(
          color: isNew 
              ? DesignSystem.successColor.withValues(alpha: 0.3)
              : DesignSystem.textTertiary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.attach_file,
            size: DesignSystem.iconMd,
            color: isNew ? DesignSystem.successColor : DesignSystem.textSecondary,
          ),
          SizedBox(width: DesignSystem.spacingMd),
          Expanded(
            child: Text(
              fileName,
              style: DesignSystem.bodyMedium,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (isNew)
            IconButton(
              onPressed: () {
                setState(() {
                  _uploadedFiles.removeWhere((file) => file.name == fileName);
                });
              },
              icon: Icon(
                Icons.close,
                size: DesignSystem.iconMd,
                color: DesignSystem.errorColor,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      margin: EdgeInsets.all(DesignSystem.spacing2xl),
      child: BlocConsumer<CreateAssignmentCubit, CreateAssignmentState>(
        listener: (context, state) {
          if (state is CreateAssignmentSuccess) {
            UiUtils.showBottomToastOverlay(
              context: context,
              errorMessage: "Assignment created successfully",
              backgroundColor: DesignSystem.successColor,
            );
            Navigator.of(context).pop(true);
          } else if (state is CreateAssignmentFailure) {
            UiUtils.showBottomToastOverlay(
              context: context,
              errorMessage: state.errormessage,
              backgroundColor: DesignSystem.errorColor,
            );
          }
        },
        builder: (context, state) {
          final isLoading = state is CreateAssignmentInProcess;
          
          return SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: isLoading ? null : _submitAssignment,
              style: DesignSystem.primaryButtonStyle,
              child: isLoading
                  ? SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          DesignSystem.textOnPrimary,
                        ),
                      ),
                    )
                  : Text(
                      widget.isEditing 
                          ? UiUtils.getTranslatedLabel(context, updateKey)
                          : "Create Assignment",
                      style: DesignSystem.labelLarge.copyWith(
                        color: DesignSystem.textOnPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: DesignSystem.primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: DesignSystem.primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  Future<void> _addFiles() async {
    try {
      final permission = await Permission.storage.request();
      if (permission.isGranted) {
        await _pickFiles();
      } else {
        await _pickFiles();
      }
    } catch (e) {
      if (mounted) {
        UiUtils.showBottomToastOverlay(
          context: context,
          errorMessage: "Storage permission required to upload files",
          backgroundColor: DesignSystem.errorColor,
        );
      }
    }
  }

  Future<void> _pickFiles() async {
    final FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
    );

    if (result != null) {
      setState(() {
        _uploadedFiles.addAll(result.files);
      });
    }
  }

  void _submitAssignment() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedDate == null || _selectedTime == null) {
      UiUtils.showBottomToastOverlay(
        context: context,
        errorMessage: "Please select due date and time",
        backgroundColor: DesignSystem.errorColor,
      );
      return;
    }

    final dueDateTime = DateTime(
      _selectedDate!.year,
      _selectedDate!.month,
      _selectedDate!.day,
      _selectedTime!.hour,
      _selectedTime!.minute,
    );

    // Create assignment using the actual cubit method signature
    context.read<CreateAssignmentCubit>().createAssignment(
      classId: 1, // TODO: Get from selected class section
      subjectId: 1, // TODO: Get from selected subject
      name: _nameController.text.trim(),
      instruction: _instructionsController.text.trim(),
      datetime: dueDateTime.toIso8601String(),
      points: _pointsController.text.trim(),
      resubmission: _allowResubmission,
      extraDayForResubmission: _resubmissionDaysController.text.trim(),
      file: _uploadedFiles.isNotEmpty ? _uploadedFiles : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.backgroundColor,
      body: Column(
        children: [
          _buildAppBar(),
          Expanded(
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                controller: _scrollController,
                padding: EdgeInsets.only(bottom: DesignSystem.spacing2xl),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: DesignSystem.spacingLg),
                    
                    _buildSectionHeader("Basic Information", Icons.info_outline),
                    _buildTextField(
                      label: UiUtils.getTranslatedLabel(context, assignmentNameKey),
                      controller: _nameController,
                      hint: "Enter assignment name",
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return "Assignment name is required";
                        }
                        return null;
                      },
                    ),
                    
                    _buildTextField(
                      label: UiUtils.getTranslatedLabel(context, instructionsKey),
                      controller: _instructionsController,
                      hint: "Enter assignment instructions",
                      maxLines: 4,
                    ),
                    
                    _buildTextField(
                      label: UiUtils.getTranslatedLabel(context, pointsKey),
                      controller: _pointsController,
                      hint: "Enter points (optional)",
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                    
                    SizedBox(height: DesignSystem.spacingLg),
                    _buildSectionHeader("Due Date & Time", Icons.schedule),
                    _buildDateTimeSelector(),
                    
                    SizedBox(height: DesignSystem.spacingLg),
                    _buildSectionHeader("Settings", Icons.settings),
                    _buildResubmissionToggle(),
                    
                    SizedBox(height: DesignSystem.spacingLg),
                    _buildSectionHeader("Attachments", Icons.attach_file),
                    _buildFileUploadSection(),
                    
                    SizedBox(height: DesignSystem.spacing3xl),
                    _buildSubmitButton(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 