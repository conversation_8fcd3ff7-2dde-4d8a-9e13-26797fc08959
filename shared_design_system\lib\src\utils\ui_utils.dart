import 'package:flutter/material.dart';
import '../tokens/spacing.dart';

/// Utility functions for common UI operations
class UiUtils {
  UiUtils._();

  /// Get screen width
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// Get screen height
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// Check if screen is mobile size
  static bool isMobile(BuildContext context) {
    return getScreenWidth(context) < AppSpacing.mobileBreakpoint;
  }

  /// Check if screen is tablet size
  static bool isTablet(BuildContext context) {
    final width = getScreenWidth(context);
    return width >= AppSpacing.mobileBreakpoint && 
           width < AppSpacing.tabletBreakpoint;
  }

  /// Check if screen is desktop size
  static bool isDesktop(BuildContext context) {
    return getScreenWidth(context) >= AppSpacing.tabletBreakpoint;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(AppSpacing.medium);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(AppSpacing.large);
    } else {
      return const EdgeInsets.all(AppSpacing.extraLarge);
    }
  }

  /// Get responsive horizontal padding
  static EdgeInsets getResponsiveHorizontalPadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.symmetric(horizontal: AppSpacing.medium);
    } else if (isTablet(context)) {
      return const EdgeInsets.symmetric(horizontal: AppSpacing.large);
    } else {
      return const EdgeInsets.symmetric(horizontal: AppSpacing.extraLarge);
    }
  }

  /// Get safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// Get status bar height
  static double getStatusBarHeight(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// Get bottom safe area height
  static double getBottomSafeAreaHeight(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  /// Get keyboard height
  static double getKeyboardHeight(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom;
  }

  /// Check if keyboard is visible
  static bool isKeyboardVisible(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom > 0;
  }

  /// Get app bar height including safe area
  static double getAppBarHeightWithSafeArea(BuildContext context) {
    return AppSpacing.appBarHeight + getStatusBarHeight(context);
  }

  /// Create vertical spacing widget
  static Widget verticalSpace(double height) {
    return SizedBox(height: height);
  }

  /// Create horizontal spacing widget
  static Widget horizontalSpace(double width) {
    return SizedBox(width: width);
  }

  /// Create small vertical spacing
  static Widget get smallVerticalSpace => verticalSpace(AppSpacing.small);

  /// Create medium vertical spacing
  static Widget get mediumVerticalSpace => verticalSpace(AppSpacing.medium);

  /// Create large vertical spacing
  static Widget get largeVerticalSpace => verticalSpace(AppSpacing.large);

  /// Create small horizontal spacing
  static Widget get smallHorizontalSpace => horizontalSpace(AppSpacing.small);

  /// Create medium horizontal spacing
  static Widget get mediumHorizontalSpace => horizontalSpace(AppSpacing.medium);

  /// Create large horizontal spacing
  static Widget get largeHorizontalSpace => horizontalSpace(AppSpacing.large);

  /// Calculate responsive grid columns
  static int getResponsiveGridColumns(BuildContext context, {
    double itemWidth = 300,
    int minColumns = 1,
    int maxColumns = 4,
  }) {
    final screenWidth = getScreenWidth(context);
    final padding = getResponsiveHorizontalPadding(context).horizontal;
    final availableWidth = screenWidth - padding;
    
    final columns = (availableWidth / itemWidth).floor();
    return columns.clamp(minColumns, maxColumns);
  }

  /// Show snackbar with consistent styling
  static void showSnackBar(
    BuildContext context,
    String message, {
    SnackBarAction? action,
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        action: action,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(AppSpacing.medium),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppSpacing.radiusMd),
        ),
      ),
    );
  }

  /// Dismiss keyboard
  static void dismissKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }

  /// Focus next input field
  static void focusNext(BuildContext context) {
    FocusScope.of(context).nextFocus();
  }

  /// Focus previous input field
  static void focusPrevious(BuildContext context) {
    FocusScope.of(context).previousFocus();
  }
} 