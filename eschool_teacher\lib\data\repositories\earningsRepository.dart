import 'package:eschool_teacher/data/models/earnings.dart';
import 'package:eschool_teacher/data/models/transaction.dart';
import 'package:eschool_teacher/data/models/withdrawal_request.dart';
import 'package:eschool_teacher/utils/api.dart';
import 'package:eschool_teacher/utils/dummy_data_utils.dart';
import 'package:eschool_teacher/utils/errorMessageKeys.dart';
import 'package:eschool_teacher/utils/hiveBoxKeys.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

class EarningsRepository {
  // Fetch teacher's earnings summary including available balance, total earnings, etc.
  Future<Map<String, dynamic>> fetchEarnings() async {
    try {
      final response = await Api.get(
        url: Api.teacherEarnings,
        useAuthToken: true,
      );

      if (response['status']) {
        return {
          "earnings": Earnings.fromJson(response['data']),
          "error": false,
        };
      }
      
      // Return dummy data since API isn't ready
      debugPrint("API not ready yet, using dummy earnings data");
      return {
        "earnings": DummyDataUtils.getDummyEarnings(),
        "error": false,
      };
    } catch (e) {
      debugPrint("Error fetchEarnings: $e");
      // Fallback to dummy data
      return {
        "earnings": DummyDataUtils.getDummyEarnings(),
        "error": false,
      };
    }
  }

  // Fetch teacher's transaction history for income/payments
  Future<Map<String, dynamic>> fetchTransactions({
    required int page,
    String? type,
    String? startDate,
    String? endDate,
  }) async {
    try {
      // Build query parameters
      Map<String, dynamic> queryParams = {"page": page.toString()};
      if (type != null) queryParams["type"] = type;
      if (startDate != null) queryParams["start_date"] = startDate;
      if (endDate != null) queryParams["end_date"] = endDate;
      
      final response = await Api.get(
        url: Api.teacherTransactions,
        useAuthToken: true,
        queryParameters: queryParams,
      );

      if (response['status']) {
        final List<Transaction> transactions = (response['data']['transactions'] as List)
            .map((transaction) => Transaction.fromJson(transaction))
            .toList();

        return {
          "transactions": transactions,
          "totalPages": response['data']['totalPages'] ?? 1,
          "currentPage": response['data']['currentPage'] ?? 1,
          "error": false,
        };
      }
      
      // Return dummy data
      debugPrint("API not ready yet, using dummy transactions data");
      return {
        "transactions": DummyDataUtils.getDummyEarnings().transactions,
        "totalPages": 1,
        "currentPage": 1,
        "error": false,
      };
    } catch (e) {
      debugPrint("Error fetchTransactions: $e");
      // Fallback to dummy data
      return {
        "transactions": DummyDataUtils.getDummyEarnings().transactions,
        "totalPages": 1,
        "currentPage": 1,
        "error": false,
      };
    }
  }

  // Fetch teacher's withdrawal requests
  Future<Map<String, dynamic>> fetchWithdrawalRequests({
    required int page,
    String? status,
  }) async {
    try {
      // Build query parameters
      Map<String, dynamic> queryParams = {"page": page.toString()};
      if (status != null) queryParams["status"] = status;
      
      final response = await Api.get(
        url: Api.withdrawalRequests,
        useAuthToken: true,
        queryParameters: queryParams,
      );

      if (response['status']) {
        final List<WithdrawalRequest> withdrawalRequests = 
            (response['data']['withdrawalRequests'] as List)
                .map((request) => WithdrawalRequest.fromJson(request))
                .toList();

        return {
          "withdrawalRequests": withdrawalRequests,
          "totalPages": response['data']['totalPages'] ?? 1,
          "currentPage": response['data']['currentPage'] ?? 1,
          "error": false,
        };
      }
      
      return {
        "withdrawalRequests": <WithdrawalRequest>[],
        "error": true,
        "message": response['message'] ?? errorMessageKey,
      };
    } catch (e) {
      debugPrint("Error fetchWithdrawalRequests: $e");
      return {
        "withdrawalRequests": <WithdrawalRequest>[],
        "error": true,
        "message": errorMessageKey,
      };
    }
  }

  // Create a new withdrawal request
  Future<Map<String, dynamic>> requestWithdrawal({
    required double amount,
    required String iban,
  }) async {
    try {
      final response = await Api.post(
        url: Api.withdrawalRequest,
        useAuthToken: true,
        body: {
          "amount": amount.toString(),
          "iban": iban,
        },
      );

      if (response['status']) {
        return {
          "success": true,
          "withdrawalRequest": WithdrawalRequest.fromJson(response['data']['request']),
          "error": false,
        };
      }
      
      return {
        "success": false,
        "error": true,
        "message": response['message'] ?? errorMessageKey,
      };
    } catch (e) {
      debugPrint("Error requestWithdrawal: $e");
      return {
        "success": false,
        "error": true,
        "message": errorMessageKey,
      };
    }
  }

  // Get teacher's IBAN from profile
  Future<Map<String, dynamic>> getIban() async {
    try {
      final response = await Api.get(
        url: Api.getIban,
        useAuthToken: true,
      );

      if (response['status']) {
        String iban = response['data']['iban'] ?? "";
        
        // Store IBAN locally for quick access
        await Hive.box(settingsBoxKey).put('teacher_iban', iban);
        
        return {
          "iban": iban,
          "error": false,
        };
      }
      
      return {
        "iban": "",
        "error": true,
        "message": response['message'] ?? errorMessageKey,
      };
    } catch (e) {
      debugPrint("Error getIban: $e");
      
      // Try to get from local cache if API fails
      final String cachedIban = Hive.box(settingsBoxKey).get('teacher_iban', defaultValue: "");
      if (cachedIban.isNotEmpty) {
        return {
          "iban": cachedIban,
          "error": false,
        };
      }
      
      return {
        "iban": "",
        "error": true,
        "message": errorMessageKey,
      };
    }
  }

  // Update teacher's IBAN in profile
  Future<Map<String, dynamic>> updateIban({
    required String iban,
  }) async {
    try {
      final response = await Api.post(
        url: Api.updateIban,
        useAuthToken: true,
        body: {
          "iban": iban,
        },
      );

      if (response['status']) {
        // Update local cache
        await Hive.box(settingsBoxKey).put('teacher_iban', iban);
        
        return {
          "success": true,
          "error": false,
        };
      }
      
      return {
        "success": false,
        "error": true,
        "message": response['message'] ?? errorMessageKey,
      };
    } catch (e) {
      debugPrint("Error updateIban: $e");
      return {
        "success": false,
        "error": true,
        "message": errorMessageKey,
      };
    }
  }

  // Get teacher's payment preference settings
  Future<Map<String, dynamic>> getPaymentSettings() async {
    try {
      final response = await Api.get(
        url: Api.teacherProfile,
        useAuthToken: true,
      );

      if (response['status']) {
        String bankName = response['data']['bank_name'] ?? "";
        String accountNumber = response['data']['account_number'] ?? "";
        
        // Store locally for quick access
        await Hive.box(settingsBoxKey).put('teacher_bank_name', bankName);
        await Hive.box(settingsBoxKey).put('teacher_account_number', accountNumber);
        
        return {
          "bankName": bankName,
          "accountNumber": accountNumber,
          "error": false,
        };
      }
      
      // Return dummy data
      return {
        "bankName": "Example Bank",
        "accountNumber": "XXXX-XXXX-1234",
        "error": false,
      };
    } catch (e) {
      debugPrint("Error getPaymentSettings: $e");
      
      // Try to get from local cache if API fails
      final String cachedBankName = Hive.box(settingsBoxKey).get('teacher_bank_name', defaultValue: "");
      final String cachedAccountNumber = Hive.box(settingsBoxKey).get('teacher_account_number', defaultValue: "");
      
      if (cachedBankName.isNotEmpty || cachedAccountNumber.isNotEmpty) {
        return {
          "bankName": cachedBankName,
          "accountNumber": cachedAccountNumber,
          "error": false,
        };
      }
      
      // Return dummy data
      return {
        "bankName": "Example Bank",
        "accountNumber": "XXXX-XXXX-1234",
        "error": false,
      };
    }
  }

  // Update teacher's payment preferences
  Future<Map<String, dynamic>> updatePaymentSettings({
    required String bankName,
    required String accountNumber,
  }) async {
    try {
      final response = await Api.post(
        url: Api.updateProfile,
        useAuthToken: true,
        body: {
          "bank_name": bankName,
          "account_number": accountNumber,
        },
      );

      if (response['status']) {
        // Update local cache
        await Hive.box(settingsBoxKey).put('teacher_bank_name', bankName);
        await Hive.box(settingsBoxKey).put('teacher_account_number', accountNumber);
        
        return {
          "success": true,
          "error": false,
        };
      }
      
      // Simulate success for now
      debugPrint("API not ready yet, simulating successful payment settings update");
      // Update local cache anyway
      await Hive.box(settingsBoxKey).put('teacher_bank_name', bankName);
      await Hive.box(settingsBoxKey).put('teacher_account_number', accountNumber);
      
      return {
        "success": true,
        "error": false,
      };
    } catch (e) {
      debugPrint("Error updatePaymentSettings: $e");
      
      // Simulate success for testing
      // Update local cache anyway
      await Hive.box(settingsBoxKey).put('teacher_bank_name', bankName);
      await Hive.box(settingsBoxKey).put('teacher_account_number', accountNumber);
      
      return {
        "success": true,
        "error": false,
      };
    }
  }
} 