import 'package:eschool_teacher/app/routes.dart';
import 'package:eschool_teacher/cubits/authCubit.dart';
import 'package:eschool_teacher/cubits/forgotPasswordRequestCubit.dart';
import 'package:eschool_teacher/cubits/signInCubit.dart';
import 'package:eschool_teacher/data/repositories/authRepository.dart';
import 'package:eschool_teacher/data/repositories/settingsRepository.dart';
import 'package:eschool_teacher/ui/screens/login/widgets/forgotPasswordRequestBottomsheet.dart';
import 'package:eschool_teacher/ui/screens/login/widgets/termsAndConditionAndPrivacyPolicyContainer.dart';
import 'package:eschool_teacher/ui/styles/designSystem.dart';
import 'package:eschool_teacher/ui/widgets/customCircularProgressIndicator.dart';
import 'package:eschool_teacher/ui/widgets/customRoundedButton.dart';
import 'package:eschool_teacher/ui/widgets/passwordHideShowButton.dart';
import 'package:eschool_teacher/utils/constants.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();

  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => BlocProvider<SignInCubit>(
        child: const LoginScreen(),
        create: (_) => SignInCubit(AuthRepository()),
      ),
    );
  }
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  late final AnimationController _animationController = AnimationController(
    vsync: this,
    duration: DesignSystem.animationSlow,
  );

  late final Animation<double> _fadeAnimation =
      Tween<double>(begin: 0.0, end: 1.0).animate(
    CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ),
  );

  late final Animation<Offset> _slideAnimation =
      Tween<Offset>(begin: const Offset(0.0, 0.3), end: Offset.zero).animate(
    CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ),
  );

  bool _hidePassword = true;

  final TextEditingController _emailTextEditingController =
      TextEditingController(
    text: showDefaultCredentials ? defaultTeacherEmail : null,
  );

  final TextEditingController _passwordTextEditingController =
      TextEditingController(
    text: showDefaultCredentials ? defaultTeacherPassword : null,
  );

  @override
  void initState() {
    super.initState();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _emailTextEditingController.dispose();
    _passwordTextEditingController.dispose();
    super.dispose();
  }

  void _signInTeacher() {
    if (_emailTextEditingController.text.trim().isEmpty) {
      UiUtils.showBottomToastOverlay(
        context: context,
        errorMessage: UiUtils.getTranslatedLabel(context, pleaseEnterEmailKey),
        backgroundColor: DesignSystem.errorColor,
      );
      return;
    }

    if (_passwordTextEditingController.text.trim().isEmpty) {
      UiUtils.showBottomToastOverlay(
        context: context,
        errorMessage:
            UiUtils.getTranslatedLabel(context, pleaseEnterPasswordKey),
        backgroundColor: DesignSystem.errorColor,
      );
      return;
    }

    context.read<SignInCubit>().signInUser(
          email: _emailTextEditingController.text.trim(),
          password: _passwordTextEditingController.text.trim(),
        );
  }

  Widget _buildBackgroundPattern() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF4CAF50), // Green
            Color(0xFF81C784), // Light Green
          ],
        ),
      ),
      child: CustomPaint(
        painter: BackgroundPatternPainter(),
        size: Size.infinite,
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          UiUtils.getTranslatedLabel(context, letsSignInKey),
          style: DesignSystem.headingLarge.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(height: DesignSystem.spacingMd),
        Text(
          "${UiUtils.getTranslatedLabel(context, welcomeBackKey)},\n${UiUtils.getTranslatedLabel(context, youHaveBeenMissedKey)}",
          style: DesignSystem.bodyLarge.copyWith(
            color: DesignSystem.textSecondary,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildEmailField() {
    return Container(
      decoration: BoxDecoration(
        color: DesignSystem.surfaceColor,
        borderRadius: DesignSystem.borderRadiusLg,
        border: Border.all(
          color: DesignSystem.textTertiary,
          width: 1,
        ),
        boxShadow: DesignSystem.shadowSm,
      ),
      child: TextFormField(
        controller: _emailTextEditingController,
        keyboardType: TextInputType.emailAddress,
        style: DesignSystem.bodyMedium,
        decoration: DesignSystem.inputDecoration(
          hintText: UiUtils.getTranslatedLabel(context, emailKey),
          prefixIcon: Container(
            padding: DesignSystem.paddingMd,
            child: SvgPicture.asset(
              UiUtils.getImagePath("mail_icon.svg"),
              width: DesignSystem.iconMd,
              height: DesignSystem.iconMd,
              colorFilter: const ColorFilter.mode(
                DesignSystem.textSecondary,
                BlendMode.srcIn,
              ),
            ),
          ),
        ).copyWith(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          fillColor: Colors.transparent,
          filled: false,
        ),
      ),
    );
  }

  Widget _buildPasswordField() {
    return Container(
      decoration: BoxDecoration(
        color: DesignSystem.surfaceColor,
        borderRadius: DesignSystem.borderRadiusLg,
        border: Border.all(
          color: DesignSystem.textTertiary,
          width: 1,
        ),
        boxShadow: DesignSystem.shadowSm,
      ),
      child: TextFormField(
        controller: _passwordTextEditingController,
        obscureText: _hidePassword,
        style: DesignSystem.bodyMedium,
        decoration: DesignSystem.inputDecoration(
          hintText: UiUtils.getTranslatedLabel(context, passwordKey),
          prefixIcon: Container(
            padding: DesignSystem.paddingMd,
            child: const Icon(
              Icons.lock_outline,
              color: DesignSystem.textSecondary,
              size: DesignSystem.iconMd,
            ),
          ),
          suffixIcon: PasswordHideShowButton(
            hidePassword: _hidePassword,
            onTap: () {
              setState(() {
                _hidePassword = !_hidePassword;
              });
            },
          ),
        ).copyWith(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          fillColor: Colors.transparent,
          filled: false,
        ),
      ),
    );
  }

  Widget _buildForgotPassword() {
    return Align(
      alignment: AlignmentDirectional.centerEnd,
      child: TextButton(
        onPressed: () {
          if (context.read<SignInCubit>().state is SignInInProgress) {
            return;
          }
          if (UiUtils.isDemoVersionEnable(context: context)) {
            UiUtils.showFeatureDisableInDemoVersion(context);
            return;
          }
          UiUtils.showBottomSheet(
            child: BlocProvider(
              create: (_) => ForgotPasswordRequestCubit(AuthRepository()),
              child: const ForgotPasswordRequestBottomsheet(),
            ),
            context: context,
          ).then((value) {
            if (value != null && !value['error']) {
              if (context.mounted) {
                UiUtils.showBottomToastOverlay(
                  context: context,
                  errorMessage:
                      "${UiUtils.getTranslatedLabel(context, passwordUpdateLinkSentKey)} ${value['email']}",
                  backgroundColor: DesignSystem.successColor,
                );
              }
            }
          });
        },
        child: Text(
          "${UiUtils.getTranslatedLabel(context, forgotPasswordKey)}?",
          style: DesignSystem.bodyMedium.copyWith(
            color: DesignSystem.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildSignInButton() {
    return BlocConsumer<SignInCubit, SignInState>(
      listener: (context, state) {
        if (state is SignInSuccess) {
          context.read<AuthCubit>().authenticateUser(
                jwtToken: state.jwtToken,
                teacher: state.teacher,
              );
          SettingsRepository().setNotificationCount(0);
          Navigator.of(context).pushReplacementNamed(Routes.home);
        } else if (state is SignInFailure) {
          UiUtils.showBottomToastOverlay(
            context: context,
            errorMessage: UiUtils.getErrorMessageFromErrorCode(
              context,
              state.errorMessage,
            ),
            backgroundColor: DesignSystem.errorColor,
          );
        }
      },
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: () {
              if (state is SignInInProgress) return;
              FocusScope.of(context).unfocus();
              _signInTeacher();
            },
            style: DesignSystem.primaryButtonStyle.copyWith(
              shape: WidgetStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: DesignSystem.borderRadiusLg,
                ),
              ),
            ),
            child: state is SignInInProgress
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        DesignSystem.textOnPrimary,
                      ),
                    ),
                  )
                : Text(
                    UiUtils.getTranslatedLabel(context, signInKey),
                    style: DesignSystem.labelLarge.copyWith(
                      color: DesignSystem.textOnPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        );
      },
    );
  }

  Widget _buildLoginForm() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacing3xl,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          SizedBox(height: DesignSystem.spacing4xl),
          _buildEmailField(),
          SizedBox(height: DesignSystem.spacing2xl),
          _buildPasswordField(),
          SizedBox(height: DesignSystem.spacingMd),
          _buildForgotPassword(),
          SizedBox(height: DesignSystem.spacing3xl),
          _buildSignInButton(),
          SizedBox(height: DesignSystem.spacing2xl),
          const TermsAndConditionAndPrivacyPolicyContainer(),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DesignSystem.backgroundColor,
      body: SafeArea(
        child: Stack(
          children: [
            _buildBackgroundPattern(),
            SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height -
                      MediaQuery.of(context).padding.top,
                ),
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(height: DesignSystem.spacing5xl),
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: DesignSystem.surfaceColor,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(DesignSystem.radius3xl),
                              topRight: Radius.circular(DesignSystem.radius3xl),
                            ),
                          ),
                          child: _buildLoginForm(),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class BackgroundPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    // Draw organic shapes
    final path1 = Path();
    path1.moveTo(0, size.height * 0.3);
    path1.quadraticBezierTo(
      size.width * 0.3,
      size.height * 0.1,
      size.width * 0.7,
      size.height * 0.2,
    );
    path1.quadraticBezierTo(
      size.width * 0.9,
      size.height * 0.25,
      size.width,
      size.height * 0.1,
    );
    path1.lineTo(size.width, 0);
    path1.lineTo(0, 0);
    path1.close();

    canvas.drawPath(path1, paint);

    final path2 = Path();
    path2.moveTo(size.width, size.height * 0.7);
    path2.quadraticBezierTo(
      size.width * 0.7,
      size.height * 0.9,
      size.width * 0.3,
      size.height * 0.8,
    );
    path2.quadraticBezierTo(
      size.width * 0.1,
      size.height * 0.75,
      0,
      size.height * 0.9,
    );
    path2.lineTo(0, size.height);
    path2.lineTo(size.width, size.height);
    path2.close();

    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
