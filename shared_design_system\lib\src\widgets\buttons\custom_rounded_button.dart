import 'package:flutter/material.dart';
import '../../tokens/colors.dart';
import '../../tokens/spacing.dart';
import '../../tokens/typography.dart';
import '../../tokens/shadows.dart';

/// Customizable rounded button following the design system
class CustomRoundedButton extends StatelessWidget {
  const CustomRoundedButton({
    super.key,
    required this.onPressed,
    required this.buttonName,
    this.backgroundColor,
    this.foregroundColor,
    this.borderColor,
    this.borderWidth = 0,
    this.elevation,
    this.width,
    this.height,
    this.fontSize,
    this.fontWeight,
    this.radius,
    this.child,
    this.padding,
    this.isLoading = false,
    this.loadingWidget,
    this.icon,
    this.iconAlignment = IconAlignment.start,
    this.textAlign = TextAlign.center,
    this.disabled = false,
    this.showShadow = true, Color? textColor,
  });

  /// Button press callback
  final VoidCallback? onPressed;

  /// Button text (ignored if child is provided)
  final String buttonName;

  /// Custom child widget (overrides buttonName)
  final Widget? child;

  /// Background color (defaults to primary)
  final Color? backgroundColor;

  /// Text/icon color (defaults to onPrimary)
  final Color? foregroundColor;

  /// Border color
  final Color? borderColor;

  /// Border width
  final double borderWidth;

  /// Button elevation
  final double? elevation;

  /// Button width
  final double? width;

  /// Button height
  final double? height;

  /// Text font size
  final double? fontSize;

  /// Text font weight
  final FontWeight? fontWeight;

  /// Border radius
  final double? radius;

  /// Internal padding
  final EdgeInsetsGeometry? padding;

  /// Loading state
  final bool isLoading;

  /// Custom loading widget
  final Widget? loadingWidget;

  /// Leading or trailing icon
  final Widget? icon;

  /// Icon alignment
  final IconAlignment iconAlignment;

  /// Text alignment
  final TextAlign textAlign;

  /// Disabled state
  final bool disabled;

  /// Show shadow
  final bool showShadow;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Determine colors
    final effectiveBackgroundColor = backgroundColor ?? 
      (disabled ? AppColors.neutral300 : AppColors.primary);
    final effectiveForegroundColor = foregroundColor ?? 
      (disabled ? AppColors.neutral600 : AppColors.onPrimary);
    final effectiveBorderColor = borderColor ?? Colors.transparent;

    // Determine text style
    final textStyle = AppTypography.buttonText.copyWith(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: effectiveForegroundColor,
    );

    // Build button content
    Widget buttonContent;
    
    if (isLoading) {
      buttonContent = loadingWidget ?? SizedBox(
        width: AppSpacing.iconMd,
        height: AppSpacing.iconMd,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(effectiveForegroundColor),
        ),
      );
    } else if (child != null) {
      buttonContent = child!;
    } else if (icon != null) {
      final iconWidget = IconTheme(
        data: IconThemeData(
          color: effectiveForegroundColor,
          size: AppSpacing.iconMd,
        ),
        child: icon!,
      );
      
      final textWidget = Text(
        buttonName,
        style: textStyle,
        textAlign: textAlign,
      );

      buttonContent = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: iconAlignment == IconAlignment.start
            ? [
                iconWidget,
                const SizedBox(width: AppSpacing.sm),
                Flexible(child: textWidget),
              ]
            : [
                Flexible(child: textWidget),
                const SizedBox(width: AppSpacing.sm),
                iconWidget,
              ],
      );
    } else {
      buttonContent = Text(
        buttonName,
        style: textStyle,
        textAlign: textAlign,
      );
    }

    // Build button
    Widget button = Container(
      width: width,
      height: height ?? AppSpacing.minTouchTarget,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(radius ?? AppSpacing.buttonRadius),
        border: borderWidth > 0
            ? Border.all(color: effectiveBorderColor, width: borderWidth)
            : null,
        boxShadow: showShadow && !disabled && elevation != 0 
            ? AppShadows.custom(elevation: elevation ?? AppElevation.button)
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: disabled || isLoading ? null : onPressed,
          borderRadius: BorderRadius.circular(radius ?? AppSpacing.buttonRadius),
          child: Container(
            padding: padding ?? const EdgeInsets.symmetric(
              horizontal: AppSpacing.buttonPadding,
              vertical: AppSpacing.md,
            ),
            child: Center(child: buttonContent),
          ),
        ),
      ),
    );

    return button;
  }
}

/// Button variants for common use cases
class AppButton {
  AppButton._();

  /// Primary button
  static Widget primary({
    Key? key,
    required VoidCallback? onPressed,
    required String text,
    Widget? icon,
    IconAlignment iconAlignment = IconAlignment.start,
    double? width,
    double? height,
    bool isLoading = false,
    bool disabled = false,
  }) {
    return CustomRoundedButton(
      key: key,
      onPressed: onPressed,
      buttonName: text,
      icon: icon,
      iconAlignment: iconAlignment,
      width: width,
      height: height,
      isLoading: isLoading,
      disabled: disabled,
      backgroundColor: AppColors.primary,
      foregroundColor: AppColors.onPrimary,
    );
  }

  /// Secondary button
  static Widget secondary({
    Key? key,
    required VoidCallback? onPressed,
    required String text,
    Widget? icon,
    IconAlignment iconAlignment = IconAlignment.start,
    double? width,
    double? height,
    bool isLoading = false,
    bool disabled = false,
  }) {
    return CustomRoundedButton(
      key: key,
      onPressed: onPressed,
      buttonName: text,
      icon: icon,
      iconAlignment: iconAlignment,
      width: width,
      height: height,
      isLoading: isLoading,
      disabled: disabled,
      backgroundColor: AppColors.surfaceVariant,
      foregroundColor: AppColors.onSurface,
      showShadow: false,
    );
  }

  /// Outlined button
  static Widget outlined({
    Key? key,
    required VoidCallback? onPressed,
    required String text,
    Widget? icon,
    IconAlignment iconAlignment = IconAlignment.start,
    double? width,
    double? height,
    bool isLoading = false,
    bool disabled = false,
  }) {
    return CustomRoundedButton(
      key: key,
      onPressed: onPressed,
      buttonName: text,
      icon: icon,
      iconAlignment: iconAlignment,
      width: width,
      height: height,
      isLoading: isLoading,
      disabled: disabled,
      backgroundColor: Colors.transparent,
      foregroundColor: AppColors.primary,
      borderColor: AppColors.primary,
      borderWidth: 1,
      showShadow: false,
    );
  }

  /// Text button (no background)
  static Widget text({
    Key? key,
    required VoidCallback? onPressed,
    required String text,
    Widget? icon,
    IconAlignment iconAlignment = IconAlignment.start,
    double? width,
    double? height,
    bool isLoading = false,
    bool disabled = false,
  }) {
    return CustomRoundedButton(
      key: key,
      onPressed: onPressed,
      buttonName: text,
      icon: icon,
      iconAlignment: iconAlignment,
      width: width,
      height: height,
      isLoading: isLoading,
      disabled: disabled,
      backgroundColor: Colors.transparent,
      foregroundColor: AppColors.primary,
      showShadow: false,
    );
  }

  /// Success button
  static Widget success({
    Key? key,
    required VoidCallback? onPressed,
    required String text,
    Widget? icon,
    IconAlignment iconAlignment = IconAlignment.start,
    double? width,
    double? height,
    bool isLoading = false,
    bool disabled = false,
  }) {
    return CustomRoundedButton(
      key: key,
      onPressed: onPressed,
      buttonName: text,
      icon: icon,
      iconAlignment: iconAlignment,
      width: width,
      height: height,
      isLoading: isLoading,
      disabled: disabled,
      backgroundColor: AppColors.success,
      foregroundColor: AppColors.onPrimary,
    );
  }

  /// Warning button
  static Widget warning({
    Key? key,
    required VoidCallback? onPressed,
    required String text,
    Widget? icon,
    IconAlignment iconAlignment = IconAlignment.start,
    double? width,
    double? height,
    bool isLoading = false,
    bool disabled = false,
  }) {
    return CustomRoundedButton(
      key: key,
      onPressed: onPressed,
      buttonName: text,
      icon: icon,
      iconAlignment: iconAlignment,
      width: width,
      height: height,
      isLoading: isLoading,
      disabled: disabled,
      backgroundColor: AppColors.warning,
      foregroundColor: AppColors.onPrimary,
    );
  }

  /// Error/danger button
  static Widget error({
    Key? key,
    required VoidCallback? onPressed,
    required String text,
    Widget? icon,
    IconAlignment iconAlignment = IconAlignment.start,
    double? width,
    double? height,
    bool isLoading = false,
    bool disabled = false,
  }) {
    return CustomRoundedButton(
      key: key,
      onPressed: onPressed,
      buttonName: text,
      icon: icon,
      iconAlignment: iconAlignment,
      width: width,
      height: height,
      isLoading: isLoading,
      disabled: disabled,
      backgroundColor: AppColors.error,
      foregroundColor: AppColors.onPrimary,
    );
  }
} 