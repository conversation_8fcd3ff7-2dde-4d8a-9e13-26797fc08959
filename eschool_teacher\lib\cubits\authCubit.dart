import 'package:eschool_teacher/data/models/teacher.dart';
import 'package:eschool_teacher/data/repositories/authRepository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

abstract class AuthState {}

class AuthInitial extends AuthState {}

class Unauthenticated extends AuthState {}

class Authenticated extends AuthState {
  final String jwtToken;
  final Teacher teacher;

  Authenticated({required this.jwtToken, required this.teacher});
}

class RequestPasswordResetInProgress extends AuthState {}

class RequestPasswordResetSuccess extends AuthState {}

class RequestPasswordResetFailure extends AuthState {
  final String errorMessage;

  RequestPasswordResetFailure(this.errorMessage);
}

class VerifyResetCodeInProgress extends AuthState {}

class VerifyResetCodeSuccess extends AuthState {}

class VerifyResetCodeFailure extends AuthState {
  final String errorMessage;

  VerifyResetCodeFailure(this.errorMessage);
}

class ResetPasswordInProgress extends AuthState {}

class ResetPasswordSuc<PERSON> extends AuthState {}

class ResetPasswordFailure extends AuthState {
  final String errorMessage;

  ResetPasswordFailure(this.errorMessage);
}

class AuthCubit extends Cubit<AuthState> {
  final AuthRepository authRepository;

  AuthCubit(this.authRepository) : super(AuthInitial()) {
    _checkIsAuthenticated();
  }

  void _checkIsAuthenticated() {
    if (authRepository.getIsLogIn()) {
      emit(
        Authenticated(
          teacher: authRepository.getTeacherDetails(),
          jwtToken: authRepository.getJwtToken(),
        ),
      );
    } else {
      emit(Unauthenticated());
    }
  }

  void authenticateUser({required String jwtToken, required Teacher teacher}) {
    //
    authRepository.setJwtToken(jwtToken);
    authRepository.setIsLogIn(true);
    authRepository.setTeacherDetails(teacher);

    //emit new state
    emit(Authenticated(
      teacher: teacher,
      jwtToken: jwtToken,
    ),);
  }

  Teacher getTeacherDetails() {
    if (state is Authenticated) {
      return (state as Authenticated).teacher;
    }
    return Teacher.fromJson({});
  }

  void signOut() {
    authRepository.signOutUser();
    emit(Unauthenticated());
  }

  Future<void> requestPasswordReset({required String email}) async {
    emit(RequestPasswordResetInProgress());
    try {
      final result = await authRepository.requestPasswordReset(email);
      if (result['success']) {
        emit(RequestPasswordResetSuccess());
      } else {
        emit(RequestPasswordResetFailure(result['message']));
      }
    } catch (e) {
      emit(RequestPasswordResetFailure(e.toString()));
    }
  }

  Future<void> verifyResetCode({
    required String email,
    required String code,
  }) async {
    emit(VerifyResetCodeInProgress());
    try {
      final result = await authRepository.verifyResetCode(email, code);
      if (result['success']) {
        emit(VerifyResetCodeSuccess());
      } else {
        emit(VerifyResetCodeFailure(result['message']));
      }
    } catch (e) {
      emit(VerifyResetCodeFailure(e.toString()));
    }
  }

  Future<void> resetPassword({
    required String email,
    required String code,
    required String newPassword,
  }) async {
    emit(ResetPasswordInProgress());
    try {
      final result = await authRepository.resetPassword(email, code, newPassword);
      if (result['success']) {
        emit(ResetPasswordSuccess());
      } else {
        emit(ResetPasswordFailure(result['message']));
      }
    } catch (e) {
      emit(ResetPasswordFailure(e.toString()));
    }
  }
}
