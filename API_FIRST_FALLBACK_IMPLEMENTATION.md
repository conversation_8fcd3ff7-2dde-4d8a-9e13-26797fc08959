# API-First with Dummy Data Fallback Implementation

## Overview

This implementation provides a robust API-first approach with automatic fallback to dummy data when API calls fail and the `useDummyData` flag is enabled. This ensures the application remains functional even when the backend is unavailable.

## Flow Description

The implemented flow follows this pattern:

1. **Try API Call First** - Always attempt the real API call initially
2. **Check for Failure** - If the API call fails, check the `useDummyData` configuration
3. **Fallback to Dummy Data** - If `useDummyData` is `true`, use dummy data as fallback
4. **Show Error** - If `useDummyData` is `false`, show the original error message

## Architecture Components

### 1. ApiServiceWrapper (`lib/data/services/api_service_wrapper.dart`)

Central service that implements the API-first with fallback pattern:

```dart
// Example usage
final apiWrapper = ApiServiceWrapper();

// This will try API first, fallback to dummy data if enabled
final subjects = await apiWrapper.fetchSubjects();
final packages = await apiWrapper.fetchPackages();
final lessons = await apiWrapper.fetchLessons(subjectId: 1);
```

### 2. DummyDataService (`lib/data/services/dummy_data_service.dart`)

Enhanced with API response formatters:

```dart
// Configuration
DummyDataService.enableDummyData();  // Force dummy data mode
DummyDataService.disableDummyData(); // Disable dummy data mode
DummyDataService.setApiConnectionStatus(true); // Mark API as failed

// Get formatted responses
final subjectsResponse = DummyDataService.getSubjectsResponse();
final packagesResponse = DummyDataService.getPackagesResponse();
final lessonsResponse = DummyDataService.getLessonsResponse(subjectId);
```

### 3. Updated Repositories

All repositories now use the ApiServiceWrapper:

- **SubjectRepository** - Updated to use ApiServiceWrapper for consistent fallback
- **PackageRepository** - Updated to use ApiServiceWrapper for package fetching

### 4. EducationCenterService (`lib/data/services/education_center_service.dart`)

Unified service demonstrating the pattern:

```dart
final service = EducationCenterService();

// All these methods follow the API-first with fallback pattern
final subjects = await service.getSubjects();
final packages = await service.getPackages(subjectId: 1);
final lessons = await service.getLessons(subjectId: 1);
```

## API Endpoints Supported

Based on the Postman collection, the following endpoints are implemented:

### Authentication
- `POST /login` - User login
- `POST /register` - User registration
- `POST /logout` - User logout

### Education Data
- `GET /subjects` - Fetch all subjects
- `GET /subjects/{id}` - Fetch subject by ID
- `GET /packages` - Fetch all packages
- `GET /lessons` - Fetch lessons (with optional subject filter)
- `GET /lessons/{id}` - Fetch lesson by ID
- `GET /digital-assets` - Fetch digital marketplace items

### Email Verification & Password Reset
- `POST /email/verify` - Verify email
- `POST /resend/email/verify` - Resend verification email
- `POST /send/otp/reset/password` - Send reset OTP
- `POST /verify/otp/reset/password` - Verify reset OTP
- `POST /reset/password` - Reset password

## Usage Examples

### Basic Usage

```dart
// Initialize the service
final educationService = EducationCenterService();

// Fetch subjects (API-first with fallback)
try {
  final subjects = await educationService.getSubjects();
  print('Subjects: ${subjects['data']}');
} catch (e) {
  print('Error: $e');
}
```

### With Authentication Token

```dart
final token = 'your_auth_token';

// Fetch data with authentication
final subjects = await educationService.getSubjects(token: token);
final packages = await educationService.getPackages(token: token);
```

### Testing Different Scenarios

```dart
// 1. Normal operation (API-first)
final service = EducationCenterService();
final data1 = await service.getSubjects();

// 2. Force dummy data mode
service.enableDummyDataMode();
final data2 = await service.getSubjects(); // Will use dummy data

// 3. Simulate API failure
service.disableDummyDataMode();
service.simulateApiFailure();
final data3 = await service.getSubjects(); // Will fallback to dummy data

// 4. Reset to normal
service.resetApiStatus();
final data4 = await service.getSubjects(); // Will try API again
```

### Check Service Status

```dart
final status = service.getServiceStatus();
print('Service Status: $status');

// Output example:
// {
//   "service": "EducationCenterService",
//   "apiWrapper": {
//     "useDummyData": false,
//     "dummyDataStatus": {...},
//     "timestamp": "2024-01-01T12:00:00.000Z"
//   },
//   "dummyDataConfig": {
//     "useDummyData": false,
//     "override": false,
//     "apiConnectionFailed": false,
//     "debugMode": true
//   }
// }
```

## Configuration

### Enable/Disable Dummy Data

```dart
// Enable dummy data (will always use dummy data)
DummyDataService.enableDummyData();

// Disable dummy data (will only use dummy data on API failure)
DummyDataService.disableDummyData();

// Check current status
final useDummyData = DummyDataService.useDummyData;
```

### API Connection Status

```dart
// Mark API as failed (will trigger fallback)
DummyDataService.setApiConnectionStatus(true);

// Mark API as working (will try API calls)
DummyDataService.setApiConnectionStatus(false);
```

## Benefits

1. **Reliability** - App works even when API is down
2. **Development** - Easy testing without backend dependency
3. **Demo** - Consistent demo experience with realistic data
4. **Debugging** - Clear logging of API vs dummy data usage
5. **Flexibility** - Easy to toggle between API and dummy data modes

## Error Handling

The implementation provides clear error handling:

- **API Success** - Returns API response
- **API Failure + Dummy Data Enabled** - Returns dummy data with logging
- **API Failure + Dummy Data Disabled** - Throws original API error

## Logging

All operations are logged for debugging:

```
🔄 DummyData: Using dummy data for FETCH_SUBJECTS
ApiServiceWrapper: Attempting FETCH_SUBJECTS via API
ApiServiceWrapper: FETCH_SUBJECTS API failed: Connection timeout
ApiServiceWrapper: Falling back to dummy data for FETCH_SUBJECTS
🔄 DummyData: Using dummy data for FETCH_SUBJECTS (FALLBACK)
```

## Integration with Existing Code

The implementation is designed to be backward compatible. Existing code can gradually migrate to use the new pattern:

```dart
// Old way
final result = await Api.get(url: Api.subjects, useAuthToken: true);

// New way
final result = await ApiServiceWrapper().fetchSubjects();
```
