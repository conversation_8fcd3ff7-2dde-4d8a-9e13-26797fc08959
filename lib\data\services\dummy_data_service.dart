import 'package:flutter/foundation.dart';
import 'package:eschool/data/models/chatMessage.dart';
import 'package:eschool/data/models/subject.dart';
import 'package:eschool/data/models/subject_level.dart';
import 'package:eschool/data/models/classLesson.dart';
import 'package:eschool/data/models/package.dart';
import 'package:eschool/data/models/paymentOptions.dart';

/// Comprehensive Dummy Data Service for offline testing and fallback data
/// This service provides mock data when API is unavailable or for testing purposes
/// All dummy data is centralized here with a configurable toggle system
class DummyDataService {

  // ======== CONFIGURATION ========

  /// Override flag to force dummy data usage regardless of debug mode or API status
  static bool _useDummyDataOverride = false;

  /// API connection failure flag
  static bool _apiConnectionFailed = false;

  /// Check if app should use dummy data (for testing or when API fails)
  static bool get useDummyData => _useDummyDataOverride || kDebugMode || _apiConnectionFailed;

  /// Enable dummy data mode (override)
  static void enableDummyData() {
    _useDummyDataOverride = true;
    debugPrint('DummyData ENABLED via override');
  }

  /// Disable dummy data mode (override)
  static void disableDummyData() {
    _useDummyDataOverride = false;
    debugPrint('DummyData DISABLED via override');
  }

  /// Set API connection status
  static void setApiConnectionStatus(bool failed) {
    _apiConnectionFailed = failed;
    if (failed) {
      debugPrint('DummyData ENABLED due to API failure');
    }
  }

  /// Get current dummy data status
  static Map<String, dynamic> getStatus() {
    return {
      'useDummyData': useDummyData,
      'override': _useDummyDataOverride,
      'apiConnectionFailed': _apiConnectionFailed,
      'debugMode': kDebugMode,
    };
  }

  // ======== SUBJECTS DUMMY DATA ========

  static List<Map<String, dynamic>> get subjects => [
    {
      'id': 1,
      'title': 'اللغة العربية',
      'name': 'اللغة العربية',
      'image': 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1422&auto=format&fit=crop',
      'imageUrl': 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1422&auto=format&fit=crop',
      'bg_color': '#FF6B6B',
      'text_color': '#FFFFFF',
      'code': 'ARA101',
      'medium_id': 1,
      'type': 'Language',
      'levels': ['تأسيسي', 'إعدادي', 'ثانوي'],
      'subjectLevels': [
        {'id': 1, 'name': 'تأسيسي', 'subject_id': 1},
        {'id': 2, 'name': 'إعدادي', 'subject_id': 1},
        {'id': 3, 'name': 'ثانوي', 'subject_id': 1},
      ],
    },
    {
      'id': 2,
      'title': 'اللغة العربية للناطقين بغيرها',
      'name': 'اللغة العربية للناطقين بغيرها',
      'image': 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1422&auto=format&fit=crop',
      'imageUrl': 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1422&auto=format&fit=crop',
      'bg_color': '#4ECDC4',
      'text_color': '#FFFFFF',
      'code': 'ARAF101',
      'medium_id': 1,
      'type': 'Language',
      'levels': ['مبتدئ'],
      'subjectLevels': [
        {'id': 13, 'name': 'مبتدئ', 'subject_id': 2},
      ],
    },
    {
      'id': 3,
      'title': 'قرآن',
      'name': 'قرآن',
      'image': 'https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?q=80&w=1373&auto=format&fit=crop',
      'imageUrl': 'https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?q=80&w=1373&auto=format&fit=crop',
      'bg_color': '#45B7D1',
      'text_color': '#FFFFFF',
      'code': 'QUR101',
      'medium_id': 1,
      'type': 'Religious',
      'levels': ['حفظ', 'تصحيح تلاوة'],
      'subjectLevels': [
        {'id': 4, 'name': 'حفظ', 'subject_id': 3},
        {'id': 5, 'name': 'تصحيح تلاوة', 'subject_id': 3},
      ],
    },
    {
      'id': 4,
      'title': 'لغة إنجليزية',
      'name': 'لغة إنجليزية',
      'image': 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=1470&auto=format&fit=crop',
      'imageUrl': 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=1470&auto=format&fit=crop',
      'bg_color': '#F7DC6F',
      'text_color': '#2C3E50',
      'code': 'ENG101',
      'medium_id': 1,
      'type': 'Language',
      'levels': ['تأسيسي', 'إعدادي', 'ثانوي'],
      'subjectLevels': [
        {'id': 6, 'name': 'تأسيسي', 'subject_id': 4},
        {'id': 7, 'name': 'إعدادي', 'subject_id': 4},
        {'id': 8, 'name': 'ثانوي', 'subject_id': 4},
      ],
    },
    {
      'id': 5,
      'title': 'دورات تخصصية',
      'name': 'دورات تخصصية',
      'image': 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?q=80&w=1470&auto=format&fit=crop',
      'imageUrl': 'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?q=80&w=1470&auto=format&fit=crop',
      'bg_color': '#82E0AA',
      'text_color': '#2C3E50',
      'code': 'SPEC101',
      'medium_id': 1,
      'type': 'Specialized',
      'levels': ['specialized', 'professional'],
      'subjectLevels': [
        {'id': 9, 'name': 'specialized', 'subject_id': 5},
        {'id': 10, 'name': 'professional', 'subject_id': 5},
      ],
    },
    {
      'id': 6,
      'title': 'Mathematics',
      'name': 'Mathematics',
      'image': 'https://picsum.photos/300/200?random=1',
      'imageUrl': 'https://picsum.photos/300/200?random=1',
      'bg_color': '#9B59B6',
      'text_color': '#FFFFFF',
      'code': 'MATH101',
      'medium_id': 1,
      'type': 'Science',
      'levels': ['Basic', 'Intermediate', 'Advanced'],
      'subjectLevels': [
        {'id': 14, 'name': 'Basic', 'subject_id': 6},
        {'id': 15, 'name': 'Intermediate', 'subject_id': 6},
        {'id': 16, 'name': 'Advanced', 'subject_id': 6},
      ],
    },
  ];

  // ======== LESSONS DUMMY DATA ========

  static List<Map<String, dynamic>> get lessons => [
    // Arabic Language Lessons
    {
      'id': 1,
      'name': 'قواعد اللغة العربية الأساسية',
      'description': 'مقدمة في قواعد النحو والصرف',
      'class_section_id': 1,
      'subject_id': 1,
      'topic': [
        {
          'id': 1,
          'name': 'أقسام الكلام',
          'description': 'الاسم والفعل والحرف',
          'lesson_id': 1,
        },
        {
          'id': 2,
          'name': 'الإعراب والبناء',
          'description': 'أساسيات الإعراب في اللغة العربية',
          'lesson_id': 1,
        },
      ],
      'file': [
        {
          'id': 1,
          'file_name': 'قواعد النحو الأساسية.pdf',
          'file_url': 'https://www.example.com/arabic-grammar-basics.pdf',
          'file_thumbnail': 'https://picsum.photos/200/150?random=401',
          'file_extension': 'pdf',
          'type': '1', // file type
        },
        {
          'id': 2,
          'file_name': 'شرح قواعد النحو',
          'file_url': 'https://www.youtube.com/watch?v=arabic_grammar',
          'file_thumbnail': 'https://picsum.photos/200/150?random=402',
          'file_extension': 'mp4',
          'type': '2', // youtube video
        },
      ],
    },
    // Quran Lessons
    {
      'id': 2,
      'name': 'تجويد القرآن الكريم',
      'description': 'أحكام التجويد وقواعد التلاوة',
      'class_section_id': 1,
      'subject_id': 3,
      'topic': [
        {
          'id': 3,
          'name': 'أحكام النون الساكنة والتنوين',
          'description': 'الإظهار والإدغام والإقلاب والإخفاء',
          'lesson_id': 2,
        },
        {
          'id': 4,
          'name': 'أحكام الميم الساكنة',
          'description': 'الإخفاء الشفوي والإدغام الصغير والإظهار الشفوي',
          'lesson_id': 2,
        },
      ],
      'file': [
        {
          'id': 3,
          'file_name': 'أحكام التجويد.pdf',
          'file_url': 'https://www.example.com/tajweed-rules.pdf',
          'file_thumbnail': 'https://picsum.photos/200/150?random=403',
          'file_extension': 'pdf',
          'type': '1',
        },
      ],
    },
    // English Lessons
    {
      'id': 3,
      'name': 'English Grammar Fundamentals',
      'description': 'Basic English grammar rules and structures',
      'class_section_id': 1,
      'subject_id': 4,
      'topic': [
        {
          'id': 5,
          'name': 'Parts of Speech',
          'description': 'Nouns, verbs, adjectives, and more',
          'lesson_id': 3,
        },
        {
          'id': 6,
          'name': 'Sentence Structure',
          'description': 'Subject, predicate, and sentence types',
          'lesson_id': 3,
        },
      ],
      'file': [
        {
          'id': 4,
          'file_name': 'English Grammar Guide.pdf',
          'file_url': 'https://www.example.com/english-grammar.pdf',
          'file_thumbnail': 'https://picsum.photos/200/150?random=404',
          'file_extension': 'pdf',
          'type': '1',
        },
        {
          'id': 5,
          'file_name': 'Grammar Video Tutorial',
          'file_url': 'https://www.youtube.com/watch?v=english_grammar',
          'file_thumbnail': 'https://picsum.photos/200/150?random=405',
          'file_extension': 'mp4',
          'type': '2',
        },
      ],
    },
    // Mathematics Lessons
    {
      'id': 4,
      'name': 'Algebra Fundamentals',
      'description': 'Introduction to algebraic expressions and equations',
      'class_section_id': 1,
      'subject_id': 6,
      'topic': [
        {
          'id': 7,
          'name': 'Basic Algebraic Expressions',
          'description': 'Understanding variables and constants',
          'lesson_id': 4,
        },
        {
          'id': 8,
          'name': 'Solving Linear Equations',
          'description': 'Methods for solving first-degree equations',
          'lesson_id': 4,
        },
      ],
      'file': [
        {
          'id': 6,
          'file_name': 'Algebra Basics.pdf',
          'file_url': 'https://www.example.com/algebra-basics.pdf',
          'file_thumbnail': 'https://picsum.photos/200/150?random=406',
          'file_extension': 'pdf',
          'type': '1',
        },
      ],
    },
    // Specialized Course
    {
      'id': 5,
      'name': 'مقدمة في البرمجة',
      'description': 'أساسيات البرمجة والخوارزميات',
      'class_section_id': 1,
      'subject_id': 5,
      'topic': [
        {
          'id': 9,
          'name': 'مفاهيم البرمجة الأساسية',
          'description': 'المتغيرات والدوال والحلقات',
          'lesson_id': 5,
        },
        {
          'id': 10,
          'name': 'الخوارزميات وهياكل البيانات',
          'description': 'مقدمة في الخوارزميات الأساسية',
          'lesson_id': 5,
        },
      ],
      'file': [
        {
          'id': 7,
          'file_name': 'مقدمة في البرمجة.pdf',
          'file_url': 'https://www.example.com/programming-intro.pdf',
          'file_thumbnail': 'https://picsum.photos/200/150?random=407',
          'file_extension': 'pdf',
          'type': '1',
        },
        {
          'id': 8,
          'file_name': 'شرح البرمجة بالفيديو',
          'file_url': 'https://www.youtube.com/watch?v=programming_intro',
          'file_thumbnail': 'https://picsum.photos/200/150?random=408',
          'file_extension': 'mp4',
          'type': '2',
        },
      ],
    },
  ];

  // ======== CLASS LESSONS (LIVE SESSIONS) DUMMY DATA ========

  static List<Subject> get classLessonSubjects => [
    Subject(
      id: 1,
      title: "اللغة العربية",
      imageUrl: "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1422&auto=format&fit=crop",
      subjectLevels: [
        SubjectLevel(id: 1, name: "تأسيسي", subjectId: 1),
        SubjectLevel(id: 2, name: "إعدادي", subjectId: 1),
        SubjectLevel(id: 3, name: "ثانوي", subjectId: 1),
      ],
    ),
    Subject(
      id: 2,
      title: "اللغة العربية للناطقين بغيرها",
      imageUrl: "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1422&auto=format&fit=crop",
      subjectLevels: [
        SubjectLevel(id: 13, name: "مبتدئ", subjectId: 2),
      ],
    ),
    Subject(
      id: 3,
      title: "قرآن",
      imageUrl: "https://images.unsplash.com/photo-1456513080510-7bf3a84b82f8?q=80&w=1373&auto=format&fit=crop",
      subjectLevels: [
        SubjectLevel(id: 4, name: "حفظ", subjectId: 3),
        SubjectLevel(id: 5, name: "تصحيح تلاوة", subjectId: 3),
      ],
    ),
    Subject(
      id: 4,
      title: "لغة إنجليزية",
      imageUrl: "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=1470&auto=format&fit=crop",
      subjectLevels: [
        SubjectLevel(id: 6, name: "تأسيسي", subjectId: 4),
        SubjectLevel(id: 7, name: "إعدادي", subjectId: 4),
        SubjectLevel(id: 8, name: "ثانوي", subjectId: 4),
      ],
    ),
    Subject(
      id: 5,
      title: "دورات تخصصية",
      imageUrl: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?q=80&w=1470&auto=format&fit=crop",
      subjectLevels: [
        SubjectLevel(id: 9, name: "specialized", subjectId: 5),
        SubjectLevel(id: 10, name: "professional", subjectId: 5),
      ],
    ),
  ];

  static List<ClassLesson> get classLessons {
    final subjects = classLessonSubjects;
    final now = DateTime.now();

    return [
      // Arabic Language lessons
      ClassLesson(
        id: "1",
        title: "قواعد اللغة العربية",
        lessonSubject: subjects[0],
        levels: ["تأسيسي"],
        lessonDate: now.add(const Duration(days: 1)),
        startTime: now.add(const Duration(days: 1, hours: 10)),
        endTime: now.add(const Duration(days: 1, hours: 11)),
        teacherId: "T001",
        studentIds: [],
        meetingLink: "https://zoom.us/j/123456789",
        weeklyRepeats: 4,
      ),
      ClassLesson(
        id: "2",
        title: "النحو والصرف",
        lessonSubject: subjects[0],
        levels: ["إعدادي"],
        lessonDate: now.add(const Duration(days: 3)),
        startTime: now.add(const Duration(days: 3, hours: 11)),
        endTime: now.add(const Duration(days: 3, hours: 12)),
        teacherId: "T001",
        studentIds: [],
        meetingLink: "https://zoom.us/j/123456789",
        weeklyRepeats: 4,
      ),
      ClassLesson(
        id: "3",
        title: "البلاغة والأدب",
        lessonSubject: subjects[0],
        levels: ["ثانوي"],
        lessonDate: now.add(const Duration(days: 5)),
        startTime: now.add(const Duration(days: 5, hours: 15)),
        endTime: now.add(const Duration(days: 5, hours: 16)),
        teacherId: "T001",
        studentIds: [],
        meetingLink: "https://zoom.us/j/123456789",
        weeklyRepeats: 4,
      ),
      // Quran lessons
      ClassLesson(
        id: "4",
        title: "تجويد القرآن",
        lessonSubject: subjects[2],
        levels: ["حفظ"],
        lessonDate: now.add(const Duration(days: 1)),
        startTime: now.add(const Duration(days: 1, hours: 13)),
        endTime: now.add(const Duration(days: 1, hours: 14, minutes: 30)),
        teacherId: "T003",
        studentIds: [],
        meetingLink: "https://zoom.us/j/123123123",
        weeklyRepeats: 4,
      ),
      ClassLesson(
        id: "5",
        title: "تصحيح التلاوة",
        lessonSubject: subjects[2],
        levels: ["تصحيح تلاوة"],
        lessonDate: now.add(const Duration(days: 2)),
        startTime: now.add(const Duration(days: 2, hours: 14)),
        endTime: now.add(const Duration(days: 2, hours: 15)),
        teacherId: "T003",
        studentIds: [],
        meetingLink: "https://zoom.us/j/123123123",
        weeklyRepeats: 4,
      ),
      // English lessons
      ClassLesson(
        id: "6",
        title: "قواعد اللغة الإنجليزية",
        lessonSubject: subjects[3],
        levels: ["تأسيسي"],
        lessonDate: now.add(const Duration(days: 2)),
        startTime: now.add(const Duration(days: 2, hours: 16)),
        endTime: now.add(const Duration(days: 2, hours: 17)),
        teacherId: "T005",
        studentIds: [],
        meetingLink: "https://zoom.us/j/789789789",
        weeklyRepeats: 4,
      ),
      ClassLesson(
        id: "7",
        title: "المحادثة الإنجليزية",
        lessonSubject: subjects[3],
        levels: ["إعدادي"],
        lessonDate: now.add(const Duration(days: 4)),
        startTime: now.add(const Duration(days: 4, hours: 17)),
        endTime: now.add(const Duration(days: 4, hours: 18)),
        teacherId: "T005",
        studentIds: [],
        meetingLink: "https://zoom.us/j/789789789",
        weeklyRepeats: 4,
      ),
      ClassLesson(
        id: "8",
        title: "الأدب الإنجليزي",
        lessonSubject: subjects[3],
        levels: ["ثانوي"],
        lessonDate: now.add(const Duration(days: 6)),
        startTime: now.add(const Duration(days: 6, hours: 18)),
        endTime: now.add(const Duration(days: 6, hours: 19)),
        teacherId: "T005",
        studentIds: [],
        meetingLink: "https://zoom.us/j/789789789",
        weeklyRepeats: 4,
      ),
      // Specialized courses
      ClassLesson(
        id: "9",
        title: "مقدمة في البرمجة",
        lessonSubject: subjects[4],
        levels: ["specialized"],
        lessonDate: now.add(const Duration(days: 1)),
        startTime: now.add(const Duration(days: 1, hours: 14)),
        endTime: now.add(const Duration(days: 1, hours: 15, minutes: 30)),
        teacherId: "T007",
        studentIds: [],
        meetingLink: "https://zoom.us/j/111222333",
        weeklyRepeats: 4,
      ),
      ClassLesson(
        id: "10",
        title: "تطوير المواقع",
        lessonSubject: subjects[4],
        levels: ["professional"],
        lessonDate: now.add(const Duration(days: 3)),
        startTime: now.add(const Duration(days: 3, hours: 19)),
        endTime: now.add(const Duration(days: 3, hours: 20, minutes: 30)),
        teacherId: "T007",
        studentIds: [],
        meetingLink: "https://zoom.us/j/111222333",
        weeklyRepeats: 4,
      ),
      // Arabic for non-native speakers
      ClassLesson(
        id: "11",
        title: "العربية للمبتدئين",
        lessonSubject: subjects[1],
        levels: ["مبتدئ"],
        lessonDate: now.add(const Duration(days: 2)),
        startTime: now.add(const Duration(days: 2, hours: 12)),
        endTime: now.add(const Duration(days: 2, hours: 13)),
        teacherId: "T002",
        studentIds: [],
        meetingLink: "https://zoom.us/j/555666777",
        weeklyRepeats: 4,
      ),
      ClassLesson(
        id: "12",
        title: "المحادثة العربية",
        lessonSubject: subjects[1],
        levels: ["مبتدئ"],
        lessonDate: now.add(const Duration(days: 4)),
        startTime: now.add(const Duration(days: 4, hours: 13)),
        endTime: now.add(const Duration(days: 4, hours: 14)),
        teacherId: "T002",
        studentIds: [],
        meetingLink: "https://zoom.us/j/555666777",
        weeklyRepeats: 4,
      ),
    ];
  }

  // ======== PACKAGES DUMMY DATA ========

  static List<Map<String, dynamic>> get packages => [
    {
      'id': "3",
      'title': "باقة احترافية",
      'description': "أربع حصص في الأسبوع",
      'price': 160.0,
      'numberOfLectures': 4,
      'imageUrl': "assets/images/packages/premium.png",
      'isPopular': false,
      'features': ["أربع حصص", "مدة الحصة 45 دقيقة", "دعم فني", "مواد تعليمية إضافية", "جلسات خاصة"],
      'subjectLevels': {
        1: ["تأسيسي", "إعدادي", "ثانوي"], // اللغة العربية
        3: ["حفظ", "تصحيح تلاوة"], // قرآن
        4: ["تأسيسي", "إعدادي", "ثانوي"], // لغة إنجليزية
      },
    },
    {
      'id': "4",
      'title': "باقة ال8 دروس الاحترافية",
      'description': "ثمان حصص في الأسبوع",
      'price': 320.0,
      'numberOfLectures': 8,
      'imageUrl': "assets/images/packages/premium-plus.png",
      'isPopular': true,
      'features': ["ثمان حصص", "مدة الحصة 45 دقيقة", "دعم فني", "مواد تعليمية إضافية", "جلسات خاصة", "مذكرات تعليمية"],
      'subjectLevels': {
        1: ["تأسيسي", "إعدادي", "ثانوي"], // اللغة العربية
        3: ["حفظ", "تصحيح تلاوة"], // قرآن
        4: ["تأسيسي", "إعدادي", "ثانوي"], // لغة إنجليزية
        5: ["specialized", "professional"], // دورات تخصصية
        2: ["مبتدئ"], // اللغة العربية للناطقين بغيرها
      },
    },
    {
      'id': "5",
      'title': "باقة ال12 درس شاملة",
      'description': "اثنى عشر حصة في الأسبوع",
      'price': 480.0,
      'numberOfLectures': 12,
      'imageUrl': "assets/images/packages/premium-extra.png",
      'isPopular': false,
      'features': ["اثنى عشر حصة", "مدة الحصة 45 دقيقة", "دعم فني", "مواد تعليمية إضافية", "جلسات خاصة", "مذكرات تعليمية", "اختبارات أسبوعية"],
      'subjectLevels': {
        1: ["تأسيسي", "إعدادي", "ثانوي"], // اللغة العربية
        3: ["حفظ", "تصحيح تلاوة"], // قرآن
        4: ["تأسيسي", "إعدادي", "ثانوي"], // لغة إنجليزية
        5: ["specialized", "professional"], // دورات تخصصية
        2: ["مبتدئ"], // اللغة العربية للناطقين بغيرها
      },
    },
  ];

  // ======== API RESPONSE FORMATTERS ========

  /// Simulate network delay for realistic testing
  static Future<void> simulateNetworkDelay([int milliseconds = 1000]) async {
    await Future.delayed(Duration(milliseconds: milliseconds));
  }

  /// Log dummy data usage for debugging
  static void logDummyDataUsage(String operation) {
    debugPrint('🔄 DummyData: Using dummy data for $operation');
  }

  /// Get subjects in API response format
  static Map<String, dynamic> getSubjectsResponse() {
    return {
      'success': true,
      'data': subjects,
      'message': 'Subjects fetched successfully (dummy data)',
    };
  }

  /// Get packages in API response format
  static Map<String, dynamic> getPackagesResponse() {
    return {
      'success': true,
      'data': packages,
      'message': 'Packages fetched successfully (dummy data)',
    };
  }

  /// Get lessons in API response format
  static Map<String, dynamic> getLessonsResponse(int? subjectId) {
    List<Map<String, dynamic>> filteredLessons = lessons;

    if (subjectId != null) {
      filteredLessons = lessons.where((lesson) => lesson['subject_id'] == subjectId).toList();
    }

    return {
      'success': true,
      'data': filteredLessons,
      'message': 'Lessons fetched successfully (dummy data)',
    };
  }

  /// Get learning resources/study materials in API response format
  static Map<String, dynamic> getLearningResourcesResponse(int lessonId) {
    final lesson = lessons.firstWhere(
      (lesson) => lesson['id'] == lessonId,
      orElse: () => lessons.first,
    );

    return {
      'success': true,
      'data': {
        'lesson': lesson,
        'files': lesson['file'] ?? [],
        'topics': lesson['topic'] ?? [],
      },
      'message': 'Learning resources fetched successfully (dummy data)',
    };
  }

  /// Get digital assets in API response format
  static Map<String, dynamic> getDigitalAssetsResponse() {
    return {
      'success': true,
      'data': [
        {
          'id': 1,
          'title': 'كتاب قواعد اللغة العربية',
          'description': 'كتاب شامل لقواعد النحو والصرف',
          'type': 'book',
          'price': 25.0,
          'imageUrl': 'https://picsum.photos/300/400?random=1',
          'downloadUrl': 'https://example.com/arabic-grammar-book.pdf',
        },
        {
          'id': 2,
          'title': 'مجموعة تمارين الرياضيات',
          'description': 'تمارين متنوعة في الجبر والهندسة',
          'type': 'exercises',
          'price': 15.0,
          'imageUrl': 'https://picsum.photos/300/400?random=2',
          'downloadUrl': 'https://example.com/math-exercises.pdf',
        },
      ],
      'message': 'Digital assets fetched successfully (dummy data)',
    };
  }
}