import 'package:eschool_teacher/data/models/leave.dart';
import 'package:eschool_teacher/ui/widgets/bottomSheetTopBarMenu.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter/material.dart';

class LeaveDetailsBottomsheetContainer extends StatelessWidget {
  final Leave leave;
  const LeaveDetailsBottomsheetContainer({super.key, required this.leave});

  Widget _buildLeaveTimingContainer(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.06),
      ),
      padding: const EdgeInsets.all(15),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                UiUtils.getTranslatedLabel(context, leaveDateKey),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.secondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                UiUtils.getTranslatedLabel(context, leaveTimingKey),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.secondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Divider(
            height: 10,
            color:
                Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
          ),
          ...List.generate(
            leave.leaveDetail.length,
            (index) => Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    UiUtils.formatStringDate(leave.leaveDetail[index].date),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        color: Theme.of(context)
                            .colorScheme
                            .secondary
                            .withValues(alpha: 0.7),
                        fontSize: 16,),
                  ),
                  Text(
                    leave.leaveDetail[index].type,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        color: Theme.of(context)
                            .colorScheme
                            .secondary
                            .withValues(alpha: 0.7),
                        fontSize: 16,),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(UiUtils.bottomSheetTopRadius),
            topRight: Radius.circular(UiUtils.bottomSheetTopRadius),),
      ),
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      constraints:
          BoxConstraints(maxHeight: MediaQuery.of(context).size.height * (0.7)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          BottomSheetTopBarMenu(
              onTapCloseButton: () {
                Navigator.pop(context);
              },
              padding: EdgeInsets.only(
                  top: UiUtils.bottomSheetHorizontalContentPadding,
                  left: UiUtils.bottomSheetHorizontalContentPadding,
                  right: UiUtils.bottomSheetHorizontalContentPadding,),
              title: UiUtils.getTranslatedLabel(context, leaveDetailsKey),),
          Flexible(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                  horizontal: UiUtils.bottomSheetHorizontalContentPadding,),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 15),
                  Text(
                    UiUtils.getTranslatedLabel(context, leaveReasonKey),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        color: Theme.of(context).colorScheme.secondary,
                        fontWeight: FontWeight.w600,),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    leave.reason,
                    style: TextStyle(
                        color: Theme.of(context)
                            .colorScheme
                            .secondary
                            .withValues(alpha: 0.76),
                        fontSize: 16,),
                  ),
                  const SizedBox(height: 20),
                  _buildLeaveTimingContainer(context),
                  const SizedBox(height: 25),
                  if (leave.file.isNotEmpty) ...[
                    Text(
                      UiUtils.getTranslatedLabel(context, attachmentsKey),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: Theme.of(context).colorScheme.secondary,
                          fontWeight: FontWeight.w600,),
                    ),
                    const SizedBox(height: 5),
                    Divider(
                      height: 20,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withValues(alpha: 0.6),
                    ),
                    ...List.generate(
                      leave.file.length,
                      (index) => InkWell(
                        onTap: () {
                          UiUtils.viewOrDownloadStudyMaterial(
                              context: context,
                              studyMaterial: leave.file[index],
                              storeInExternalStorage: false,);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  leave.file[index].fileName,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,),
                                ),
                              ),
                              Icon(Icons.remove_red_eye_outlined,
                                  color:
                                      Theme.of(context).colorScheme.secondary,),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
