import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Design System for eSchool Teacher App
/// This file contains all design tokens, colors, typography, spacing, and component styles
class DesignSystem {
  // ==================== COLORS ====================
  
  /// Primary brand colors
  static const Color primaryColor = Color(0xFF5F97B7);
  static const Color primaryDark = Color(0xFF25536d);
  static const Color primaryLight = Color(0xFF8BB5D1);
  
  /// Secondary colors
  static const Color secondaryColor = Color(0xFFF4C430);
  static const Color secondaryLight = Color(0xFFF7D666);
  static const Color secondaryDark = Color(0xFFE6B800);
  
  /// Background colors
  static const Color backgroundColor = Color(0xFFe9e9e9);
  static const Color surfaceColor = Colors.white;
  static const Color cardColor = Colors.white;
  
  /// Text colors
  static const Color textPrimary = Color(0xFF2C2C2C);
  static const Color textSecondary = Color(0xFF666666);
  static const Color textTertiary = Color(0xFF999999);
  static const Color textOnPrimary = Colors.white;
  static const Color textOnSecondary = Colors.black;
  
  /// Status colors
  static const Color successColor = Color(0xFF388e3c);
  static const Color errorColor = Color(0xFFd32f2f);
  static const Color warningColor = Color(0xFFf57c00);
  static const Color infoColor = Color(0xFF1976d2);
  
  /// Class/Subject colors
  static const List<Color> classColors = [
    Color(0xff65a3fe), // Blue
    Color(0xffff6769), // Red
    Color(0xfffdb46c), // Orange
    Color(0xff3bcc97), // Green
    Color(0xff9575cd), // Purple
    Color(0xffff8a65), // Deep Orange
    Color(0xff4fc3f7), // Light Blue
    Color(0xffaed581), // Light Green
  ];
  
  /// Assignment colors
  static const Color assignmentViewColor = Color(0xff65a3fe);
  static const Color assignmentDownloadColor = Color(0xfffdb46c);
  
  /// Shimmer colors
  static final Color shimmerBase = Colors.grey.shade300;
  static final Color shimmerHighlight = Colors.grey.shade100;
  static final Color shimmerContent = Colors.white.withValues(alpha: 0.85);
  
  // ==================== TYPOGRAPHY ====================
  
  /// Font family
  static const String fontFamily = 'Rubik';
  
  /// Text styles
  static TextStyle get headingLarge => GoogleFonts.rubik(
    fontSize: 32,
    fontWeight: FontWeight.w700,
    color: textPrimary,
    height: 1.2,
  );
  
  static TextStyle get headingMedium => GoogleFonts.rubik(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: textPrimary,
    height: 1.3,
  );
  
  static TextStyle get headingSmall => GoogleFonts.rubik(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: textPrimary,
    height: 1.4,
  );
  
  static TextStyle get titleLarge => GoogleFonts.rubik(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: textPrimary,
    height: 1.4,
  );
  
  static TextStyle get titleMedium => GoogleFonts.rubik(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: textPrimary,
    height: 1.5,
  );
  
  static TextStyle get titleSmall => GoogleFonts.rubik(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: textPrimary,
    height: 1.5,
  );
  
  static TextStyle get bodyLarge => GoogleFonts.rubik(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: textPrimary,
    height: 1.5,
  );
  
  static TextStyle get bodyMedium => GoogleFonts.rubik(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: textPrimary,
    height: 1.5,
  );
  
  static TextStyle get bodySmall => GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: textSecondary,
    height: 1.5,
  );
  
  static TextStyle get labelLarge => GoogleFonts.rubik(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: textPrimary,
    height: 1.4,
  );
  
  static TextStyle get labelMedium => GoogleFonts.rubik(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: textPrimary,
    height: 1.4,
  );
  
  static TextStyle get labelSmall => GoogleFonts.rubik(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    color: textSecondary,
    height: 1.4,
  );
  
  // ==================== SPACING ====================
  
  /// Spacing constants
  static const double spacing2xs = 2.0;
  static const double spacingXs = 4.0;
  static const double spacingSm = 8.0;
  static const double spacingMd = 12.0;
  static const double spacingLg = 16.0;
  static const double spacingXl = 20.0;
  static const double spacing2xl = 24.0;
  static const double spacing3xl = 32.0;
  static const double spacing4xl = 40.0;
  static const double spacing5xl = 48.0;
  
  /// Padding constants
  static const EdgeInsets paddingXs = EdgeInsets.all(spacingXs);
  static const EdgeInsets paddingSm = EdgeInsets.all(spacingSm);
  static const EdgeInsets paddingMd = EdgeInsets.all(spacingMd);
  static const EdgeInsets paddingLg = EdgeInsets.all(spacingLg);
  static const EdgeInsets paddingXl = EdgeInsets.all(spacingXl);
  static const EdgeInsets padding2xl = EdgeInsets.all(spacing2xl);
  
  /// Margin constants
  static const EdgeInsets marginXs = EdgeInsets.all(spacingXs);
  static const EdgeInsets marginSm = EdgeInsets.all(spacingSm);
  static const EdgeInsets marginMd = EdgeInsets.all(spacingMd);
  static const EdgeInsets marginLg = EdgeInsets.all(spacingLg);
  static const EdgeInsets marginXl = EdgeInsets.all(spacingXl);
  static const EdgeInsets margin2xl = EdgeInsets.all(spacing2xl);
  
  // ==================== BORDER RADIUS ====================
  
  static const double radiusXs = 4.0;
  static const double radiusSm = 6.0;
  static const double radiusMd = 8.0;
  static const double radiusLg = 12.0;
  static const double radiusXl = 16.0;
  static const double radius2xl = 20.0;
  static const double radius3xl = 24.0;
  static const double radiusFull = 999.0;
  
  static BorderRadius get borderRadiusXs => BorderRadius.circular(radiusXs);
  static BorderRadius get borderRadiusSm => BorderRadius.circular(radiusSm);
  static BorderRadius get borderRadiusMd => BorderRadius.circular(radiusMd);
  static BorderRadius get borderRadiusLg => BorderRadius.circular(radiusLg);
  static BorderRadius get borderRadiusXl => BorderRadius.circular(radiusXl);
  static BorderRadius get borderRadius2xl => BorderRadius.circular(radius2xl);
  static BorderRadius get borderRadius3xl => BorderRadius.circular(radius3xl);
  static BorderRadius get borderRadiusFull => BorderRadius.circular(radiusFull);
  
  // ==================== SHADOWS ====================
  
  static List<BoxShadow> get shadowSm => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.05),
      offset: const Offset(0, 1),
      blurRadius: 2,
    ),
  ];
  
  static List<BoxShadow> get shadowMd => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      offset: const Offset(0, 2),
      blurRadius: 4,
    ),
  ];
  
  static List<BoxShadow> get shadowLg => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      offset: const Offset(0, 4),
      blurRadius: 8,
    ),
  ];
  
  static List<BoxShadow> get shadowXl => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.15),
      offset: const Offset(0, 8),
      blurRadius: 16,
    ),
  ];
  
  // ==================== COMPONENT STYLES ====================
  
  /// Button styles
  static ButtonStyle get primaryButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: primaryColor,
    foregroundColor: textOnPrimary,
    textStyle: labelLarge,
    padding: const EdgeInsets.symmetric(horizontal: spacing2xl, vertical: spacingLg),
    shape: RoundedRectangleBorder(borderRadius: borderRadiusLg),
    elevation: 0,
  );
  
  static ButtonStyle get secondaryButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: surfaceColor,
    foregroundColor: primaryColor,
    textStyle: labelLarge,
    padding: const EdgeInsets.symmetric(horizontal: spacing2xl, vertical: spacingLg),
    shape: RoundedRectangleBorder(
      borderRadius: borderRadiusLg,
      side: const BorderSide(color: primaryColor, width: 1),
    ),
    elevation: 0,
  );
  
  static ButtonStyle get outlineButtonStyle => OutlinedButton.styleFrom(
    foregroundColor: primaryColor,
    textStyle: labelLarge,
    padding: const EdgeInsets.symmetric(horizontal: spacing2xl, vertical: spacingLg),
    shape: RoundedRectangleBorder(borderRadius: borderRadiusLg),
    side: const BorderSide(color: primaryColor, width: 1),
  );
  
  /// Card styles
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: cardColor,
    borderRadius: borderRadiusLg,
    boxShadow: shadowMd,
  );
  
  static BoxDecoration get elevatedCardDecoration => BoxDecoration(
    color: cardColor,
    borderRadius: borderRadiusLg,
    boxShadow: shadowLg,
  );
  
  /// Input field styles
  static InputDecoration inputDecoration({
    String? hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool isError = false,
  }) => InputDecoration(
    hintText: hintText,
    labelText: labelText,
    prefixIcon: prefixIcon,
    suffixIcon: suffixIcon,
    filled: true,
    fillColor: surfaceColor,
    border: OutlineInputBorder(
      borderRadius: borderRadiusLg,
      borderSide: BorderSide(
        color: isError ? errorColor : Colors.grey.shade300,
        width: 1,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: borderRadiusLg,
      borderSide: BorderSide(
        color: Colors.grey.shade300,
        width: 1,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: borderRadiusLg,
      borderSide: const BorderSide(
        color: primaryColor,
        width: 2,
      ),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: borderRadiusLg,
      borderSide: const BorderSide(
        color: errorColor,
        width: 1,
      ),
    ),
    contentPadding: paddingLg,
    hintStyle: bodyMedium.copyWith(color: textTertiary),
    labelStyle: bodyMedium.copyWith(color: textSecondary),
  );
  
  /// App bar styles
  static AppBarTheme get appBarTheme => AppBarTheme(
    backgroundColor: primaryColor,
    foregroundColor: textOnPrimary,
    elevation: 0,
    centerTitle: true,
    titleTextStyle: titleLarge.copyWith(color: textOnPrimary),
    iconTheme: const IconThemeData(color: textOnPrimary),
  );
  
  /// Bottom navigation styles
  static BottomNavigationBarThemeData get bottomNavTheme => BottomNavigationBarThemeData(
    backgroundColor: surfaceColor,
    selectedItemColor: primaryColor,
    unselectedItemColor: textTertiary,
    type: BottomNavigationBarType.fixed,
    elevation: 8,
    selectedLabelStyle: labelSmall.copyWith(fontWeight: FontWeight.w600),
    unselectedLabelStyle: labelSmall,
  );
  
  // ==================== ICON SIZES ====================
  
  static const double iconXs = 12.0;
  static const double iconSm = 16.0;
  static const double iconMd = 20.0;
  static const double iconLg = 24.0;
  static const double iconXl = 32.0;
  static const double icon2xl = 40.0;
  static const double icon3xl = 48.0;
  
  // ==================== ANIMATION DURATIONS ====================
  
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationNormal = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  
  // ==================== BREAKPOINTS ====================
  
  static const double mobileBreakpoint = 480;
  static const double tabletBreakpoint = 768;
  static const double desktopBreakpoint = 1024;
  
  // ==================== HELPER METHODS ====================
  
  /// Get class color by index
  static Color getClassColor(int index) {
    return classColors[index % classColors.length];
  }
  
  /// Get status color
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'approved':
        return successColor;
      case 'error':
      case 'failed':
      case 'rejected':
        return errorColor;
      case 'warning':
      case 'pending':
        return warningColor;
      case 'info':
      case 'in_progress':
        return infoColor;
      default:
        return textSecondary;
    }
  }
  
  /// Create theme data
  static ThemeData createTheme() {
    return ThemeData(
      useMaterial3: true,
      fontFamily: fontFamily,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        primary: primaryColor,
        secondary: secondaryColor,
        surface: surfaceColor,
        background: backgroundColor,
        error: errorColor,
      ),
      textTheme: GoogleFonts.rubikTextTheme().copyWith(
        headlineLarge: headingLarge,
        headlineMedium: headingMedium,
        headlineSmall: headingSmall,
        titleLarge: titleLarge,
        titleMedium: titleMedium,
        titleSmall: titleSmall,
        bodyLarge: bodyLarge,
        bodyMedium: bodyMedium,
        bodySmall: bodySmall,
        labelLarge: labelLarge,
        labelMedium: labelMedium,
        labelSmall: labelSmall,
      ),
      appBarTheme: appBarTheme,
      bottomNavigationBarTheme: bottomNavTheme,
      elevatedButtonTheme: ElevatedButtonThemeData(style: primaryButtonStyle),
      outlinedButtonTheme: OutlinedButtonThemeData(style: outlineButtonStyle),
      cardTheme: CardTheme(
        color: cardColor,
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: borderRadiusLg),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: borderRadiusLg,
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        contentPadding: paddingLg,
      ),
    );
  }
} 