import 'package:flutter/material.dart';
import 'package:shared_design_system/shared_design_system.dart';

/// Demo screen showcasing the shared design system components
class DesignSystemDemoScreen extends StatefulWidget {
  const DesignSystemDemoScreen({super.key});

  @override
  State<DesignSystemDemoScreen> createState() => _DesignSystemDemoScreenState();
}

class _DesignSystemDemoScreenState extends State<DesignSystemDemoScreen> {
  final TextEditingController _textController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  int _currentNavIndex = 0;

  @override
  void dispose() {
    _textController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Design System Demo',
        actions: [
          IconButton(
            icon: const Icon(Icons.palette),
            onPressed: () {
              UiUtils.showSnackBar(
                context,
                'Design System Demo',
                action: SnackBarAction(
                  label: 'Dismiss',
                  onPressed: () {},
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Colors Section
            _buildSectionTitle('Colors'),
            _buildColorPalette(),
            UiUtils.largeVerticalSpace,

            // Typography Section
            _buildSectionTitle('Typography'),
            _buildTypographyExamples(),
            UiUtils.largeVerticalSpace,

            // Buttons Section
            _buildSectionTitle('Buttons'),
            _buildButtonExamples(),
            UiUtils.largeVerticalSpace,

            // Form Fields Section
            _buildSectionTitle('Form Fields'),
            _buildFormFieldExamples(),
            UiUtils.largeVerticalSpace,

            // Progress Indicators Section
            _buildSectionTitle('Progress Indicators'),
            _buildProgressIndicatorExamples(),
            UiUtils.largeVerticalSpace,

            // Spacing Section
            _buildSectionTitle('Spacing'),
            _buildSpacingExamples(),
          ],
        ),
      ),
      bottomNavigationBar: AppBottomNavigationBar(
        currentIndex: _currentNavIndex,
        onTap: (index) => setState(() => _currentNavIndex = index),
        items: const [
          AppBottomNavigationItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          AppBottomNavigationItem(
            icon: Icon(Icons.assignment),
            label: 'Assignments',
          ),
          AppBottomNavigationItem(
            icon: Icon(Icons.quiz),
            label: 'Exam',
          ),
          AppBottomNavigationItem(
            icon: Icon(Icons.assessment),
            label: 'Results',
          ),
          AppBottomNavigationItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppSpacing.md),
      child: Text(
        title,
        style: AppTypography.headlineMedium.copyWith(
          color: AppColors.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildColorPalette() {
    return Wrap(
      spacing: AppSpacing.sm,
      runSpacing: AppSpacing.sm,
      children: [
        _buildColorCard('Primary', AppColors.primary),
        _buildColorCard('Highlight', AppColors.highlight),
        _buildColorCard('Success', AppColors.success),
        _buildColorCard('Warning', AppColors.warning),
        _buildColorCard('Error', AppColors.error),
        _buildColorCard('Student', AppColors.studentAccent),
        _buildColorCard('Teacher', AppColors.teacherAccent),
      ],
    );
  }

  Widget _buildColorCard(String name, Color color) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppSpacing.sm),
        boxShadow: AppShadows.card,
      ),
      child: Center(
        child: Text(
          name,
          style: AppTypography.labelSmall.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget _buildTypographyExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Display Large', style: AppTypography.displayLarge),
        UiUtils.smallVerticalSpace,
        Text('Headline Large', style: AppTypography.headlineLarge),
        UiUtils.smallVerticalSpace,
        Text('Title Large', style: AppTypography.titleLarge),
        UiUtils.smallVerticalSpace,
        Text('Body Large', style: AppTypography.bodyLarge),
        UiUtils.smallVerticalSpace,
        Text('Body Medium', style: AppTypography.bodyMedium),
        UiUtils.smallVerticalSpace,
        Text('Label Medium', style: AppTypography.labelMedium),
      ],
    );
  }

  Widget _buildButtonExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomRoundedButton(
          buttonName: 'Primary Button',
          onPressed: () => _showButtonPressed('Primary'),
          backgroundColor: AppColors.primary,
          height: 50,
        ),
        UiUtils.mediumVerticalSpace,
        CustomRoundedButton(
          buttonName: 'Secondary Button',
          onPressed: () => _showButtonPressed('Secondary'),
          backgroundColor: AppColors.highlight,
          height: 50,
        ),
        UiUtils.mediumVerticalSpace,
        CustomRoundedButton(
          buttonName: 'Success Button',
          onPressed: () => _showButtonPressed('Success'),
          backgroundColor: AppColors.success,
          height: 50,
        ),
        UiUtils.mediumVerticalSpace,
        CustomRoundedButton(
          buttonName: 'Loading Button',
          onPressed: () => _toggleLoading(),
          backgroundColor: AppColors.teacherAccent,
          isLoading: _isLoading,
          height: 50,
        ),
      ],
    );
  }

  Widget _buildFormFieldExamples() {
    return Column(
      children: [
        BottomSheetTextField(
          hintText: 'Enter your name',
          textEditingController: _textController,
          labelText: 'Full Name',
          prefix: const Icon(Icons.person),
        ),
        UiUtils.mediumVerticalSpace,
        BottomSheetTextField(
          hintText: 'Enter email address',
          textEditingController: _emailController,
          labelText: 'Email',
          keyboardType: TextInputType.emailAddress,
        ),
        UiUtils.mediumVerticalSpace,
        BottomSheetTextField(
          hintText: 'Enter password',
          textEditingController: _passwordController,
          labelText: 'Password',
          hideText: !_isPasswordVisible,
          suffix: IconButton(
            icon: Icon(_isPasswordVisible ? Icons.visibility_off : Icons.visibility),
            onPressed: () {
              setState(() => _isPasswordVisible = !_isPasswordVisible);
            },
          ),
        ),
        UiUtils.mediumVerticalSpace,
        BottomSheetTextField(
          hintText: 'Search...',
          textEditingController: TextEditingController(),
          prefix: const Icon(Icons.search),
          onChanged: (value) => print('Searching: $value'),
        ),
      ],
    );
  }

  Widget _buildProgressIndicatorExamples() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            CustomCircularProgressIndicator(
              size: 24,
              color: AppColors.primary,
            ),
            CustomCircularProgressIndicator(
              size: 32,
              color: AppColors.highlight,
            ),
            CustomCircularProgressIndicator(
              size: 48,
              color: AppColors.success,
            ),
          ],
        ),
        UiUtils.mediumVerticalSpace,
        CustomCircularProgressIndicator(
          value: 0.75,
          color: AppColors.success,
          size: 60,
        ),
        UiUtils.mediumVerticalSpace,
        CustomCircularProgressIndicator(
          value: 0.45,
          color: AppColors.warning,
          size: 60,
        ),
      ],
    );
  }

  Widget _buildSpacingExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: AppSpacing.xs,
          color: AppColors.primary,
          child: const SizedBox.expand(),
        ),
        Text('XS (${AppSpacing.xs}px)', style: AppTypography.labelSmall),
        UiUtils.smallVerticalSpace,
        Container(
          height: AppSpacing.sm,
          color: AppColors.primary,
          child: const SizedBox.expand(),
        ),
        Text('SM (${AppSpacing.sm}px)', style: AppTypography.labelSmall),
        UiUtils.smallVerticalSpace,
        Container(
          height: AppSpacing.md,
          color: AppColors.primary,
          child: const SizedBox.expand(),
        ),
        Text('MD (${AppSpacing.md}px)', style: AppTypography.labelSmall),
        UiUtils.smallVerticalSpace,
        Container(
          height: AppSpacing.lg,
          color: AppColors.primary,
          child: const SizedBox.expand(),
        ),
        Text('LG (${AppSpacing.lg}px)', style: AppTypography.labelSmall),
      ],
    );
  }

  void _showButtonPressed(String buttonType) {
    UiUtils.showSnackBar(
      context,
      '$buttonType button pressed!',
    );
  }

  void _toggleLoading() {
    setState(() => _isLoading = !_isLoading);
    if (_isLoading) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() => _isLoading = false);
          UiUtils.showSnackBar(context, 'Loading completed!');
        }
      });
    }
  }
} 