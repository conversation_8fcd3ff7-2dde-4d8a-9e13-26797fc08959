import 'package:eschool_teacher/data/models/studyMaterial.dart';

enum GroupingState { individual, group }

class Lesson {
  Lesson({
    required this.id,
    required this.title,
    required this.lessonSubject,
    required this.levels,
    required this.lessonDate,
    required this.startTime,
    required this.endTime,
    required this.teacherID,
    required this.groupingState,
    required this.studentIDs,
    required this.meetingLink,
    required this.weeklyRepeats,
    // Legacy fields for backward compatibility
    this.name,
    this.description,
    this.classSectionId,
    this.subjectId,
    this.studyMaterials,
    this.topicsCount,
  });
  
  // UML diagram fields
  late final String id;
  late final String title;
  late final String lessonSubject;
  late final List<String> levels;
  late final DateTime lessonDate;
  late final DateTime startTime;
  late final DateTime endTime;
  late final String teacherID;
  late final GroupingState groupingState;
  late final List<String> studentIDs;
  late final String meetingLink;
  late final int weeklyRepeats;
  
  // Legacy fields for backward compatibility
  late final String? name;
  late final String? description;
  late final int? classSectionId;
  late final int? subjectId;
  late final List<StudyMaterial>? studyMaterials;
  late final int? topicsCount;

  Lesson.fromJson(Map<String, dynamic> json) {
    // UML diagram fields
    id = json['id']?.toString() ?? "";
    title = json['title'] ?? json['name'] ?? "";
    lessonSubject = json['lesson_subject'] ?? json['subject'] ?? "";
    levels = ((json['levels'] ?? []) as List).map((e) => e.toString()).toList();
    lessonDate = json['lesson_date'] != null 
        ? DateTime.parse(json['lesson_date']) 
        : DateTime.now();
    startTime = json['start_time'] != null 
        ? DateTime.parse(json['start_time']) 
        : DateTime.now();
    endTime = json['end_time'] != null 
        ? DateTime.parse(json['end_time']) 
        : DateTime.now();
    teacherID = json['teacher_id']?.toString() ?? "";
    groupingState = json['grouping_state'] == 'group' 
        ? GroupingState.group 
        : GroupingState.individual;
    studentIDs = ((json['student_ids'] ?? []) as List).map((e) => e.toString()).toList();
    meetingLink = json['meeting_link'] ?? "";
    weeklyRepeats = json['weekly_repeats'] ?? 0;
    
    // Legacy fields for backward compatibility
    name = json['name'];
    description = json['description'];
    classSectionId = json['class_section_id'];
    subjectId = json['subject_id'];
    topicsCount = json['topic_count'];
    studyMaterials = json['file'] != null 
        ? ((json['file'] as List).map((file) => StudyMaterial.fromJson(Map.from(file))).toList())
        : null;
  }

  // Method to check if lesson is booked (as per UML)
  bool isBooked() {
    return studentIDs.isNotEmpty;
  }
}
