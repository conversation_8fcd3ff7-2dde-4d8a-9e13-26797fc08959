import 'package:eschool_teacher/app/routes.dart';
import 'package:eschool_teacher/cubits/authCubit.dart';
import 'package:eschool_teacher/ui/styles/colors.dart';
import 'package:eschool_teacher/ui/widgets/customCircularProgressIndicator.dart';
import 'package:eschool_teacher/ui/widgets/customTextField.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

enum ResetPasswordStep {
  requestReset,
  verifyCode,
  setNewPassword,
  success,
}

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({Key? key}) : super(key: key);

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _resetCodeController = TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();

  ResetPasswordStep _currentStep = ResetPasswordStep.requestReset;
  String? _emailError;
  String? _codeError;
  String? _passwordError;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _emailController.dispose();
    _resetCodeController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _requestPasswordReset() {
    if (_emailController.text.trim().isEmpty) {
      setState(() {
        _emailError = UiUtils.getTranslatedLabel(context, pleaseEnterValidEmailKey);
      });
      return;
    }
    
    context.read<AuthCubit>().requestPasswordReset(
          email: _emailController.text.trim(),
        );
  }

  void _verifyResetCode() {
    if (_resetCodeController.text.trim().isEmpty) {
      setState(() {
        _codeError = UiUtils.getTranslatedLabel(context, 'pleaseEnterCodeKey');
      });
      return;
    }
    
    context.read<AuthCubit>().verifyResetCode(
          email: _emailController.text.trim(),
          code: _resetCodeController.text.trim(),
        );
  }

  void _resetPassword() {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    if (_newPasswordController.text != _confirmPasswordController.text) {
      setState(() {
        _passwordError = UiUtils.getTranslatedLabel(context, 'passwordsDoNotMatchKey');
      });
      return;
    }
    
    context.read<AuthCubit>().resetPassword(
          email: _emailController.text.trim(),
          code: _resetCodeController.text.trim(),
          newPassword: _newPasswordController.text,
        );
  }

  Widget _buildRequestResetStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          UiUtils.getTranslatedLabel(context, 'forgotPasswordSubtitleKey'),
          style: const TextStyle(
            fontSize: 16,
            color: bodyTextColor,
          ),
        ),
        const SizedBox(height: 30),
        CustomTextField(
          controller: _emailController,
          label: UiUtils.getTranslatedLabel(context, emailKey),
          errorText: _emailError,
          keyboardType: TextInputType.emailAddress,
          onChanged: (value) {
            if (_emailError != null) {
              setState(() {
                _emailError = null;
              });
            }
          },
        ),
        const SizedBox(height: 40),
        ElevatedButton(
          onPressed: _requestPasswordReset,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 15),
            backgroundColor: primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            UiUtils.getTranslatedLabel(context, 'sendResetLinkKey'),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVerifyCodeStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          UiUtils.getTranslatedLabel(context, 'enterCodeSentEmailKey'),
          style: const TextStyle(
            fontSize: 16,
            color: bodyTextColor,
          ),
        ),
        const SizedBox(height: 10),
        Text(
          _emailController.text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 30),
        CustomTextField(
          controller: _resetCodeController,
          label: UiUtils.getTranslatedLabel(context, 'resetCodeKey'),
          errorText: _codeError,
          keyboardType: TextInputType.number,
          maxLength: 6,
          onChanged: (value) {
            if (_codeError != null) {
              setState(() {
                _codeError = null;
              });
            }
          },
        ),
        const SizedBox(height: 40),
        ElevatedButton(
          onPressed: _verifyResetCode,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 15),
            backgroundColor: primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            UiUtils.getTranslatedLabel(context, 'verifyCodeKey'),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 20),
        TextButton(
          onPressed: _requestPasswordReset,
          child: Text(
            UiUtils.getTranslatedLabel(context, 'resendCodeKey'),
            style: const TextStyle(
              fontSize: 16,
              color: primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSetNewPasswordStep() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            UiUtils.getTranslatedLabel(context, 'setNewPasswordKey'),
            style: const TextStyle(
              fontSize: 16,
              color: bodyTextColor,
            ),
          ),
          const SizedBox(height: 30),
          CustomTextField(
            controller: _newPasswordController,
            label: UiUtils.getTranslatedLabel(context, newPasswordKey),
            obscureText: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return UiUtils.getTranslatedLabel(
                    context, pleaseEnterPasswordKey);
              }
              if (value.length < 6) {
                return UiUtils.getTranslatedLabel(
                    context, passwordLengthKey);
              }
              return null;
            },
          ),
          const SizedBox(height: 20),
          CustomTextField(
            controller: _confirmPasswordController,
            label: UiUtils.getTranslatedLabel(context, confirmPasswordKey),
            errorText: _passwordError,
            obscureText: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return UiUtils.getTranslatedLabel(
                    context, pleaseConfirmPasswordKey);
              }
              return null;
            },
            onChanged: (value) {
              if (_passwordError != null) {
                setState(() {
                  _passwordError = null;
                });
              }
            },
          ),
          const SizedBox(height: 40),
          ElevatedButton(
            onPressed: _resetPassword,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 15),
              backgroundColor: primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              UiUtils.getTranslatedLabel(context, 'resetPasswordKey'),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const Icon(
          Icons.check_circle_outline,
          size: 70,
          color: successColor,
        ),
        const SizedBox(height: 20),
        Text(
          UiUtils.getTranslatedLabel(context, 'passwordResetSuccessKey'),
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 10),
        Text(
          UiUtils.getTranslatedLabel(context, 'loginWithNewPasswordKey'),
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 16,
            color: bodyTextColor,
          ),
        ),
        const SizedBox(height: 40),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pushReplacementNamed(Routes.login);
          },
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 15),
            backgroundColor: primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            UiUtils.getTranslatedLabel(context, loginKey),
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCurrentStep() {
    switch (_currentStep) {
      case ResetPasswordStep.requestReset:
        return _buildRequestResetStep();
      case ResetPasswordStep.verifyCode:
        return _buildVerifyCodeStep();
      case ResetPasswordStep.setNewPassword:
        return _buildSetNewPasswordStep();
      case ResetPasswordStep.success:
        return _buildSuccessStep();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(UiUtils.getTranslatedLabel(context, forgotPasswordKey)),
        backgroundColor: primaryColor,
        elevation: 0,
      ),
      body: BlocConsumer<AuthCubit, AuthState>(
        listener: (context, state) {
          if (state is RequestPasswordResetSuccess) {
            setState(() {
              _currentStep = ResetPasswordStep.verifyCode;
            });
          } else if (state is RequestPasswordResetFailure) {
            UiUtils.showSnackBar(
              context: context,
              message: state.errorMessage,
              isError: true,
            );
          } else if (state is VerifyResetCodeSuccess) {
            setState(() {
              _currentStep = ResetPasswordStep.setNewPassword;
            });
          } else if (state is VerifyResetCodeFailure) {
            UiUtils.showSnackBar(
              context: context,
              message: state.errorMessage,
              isError: true,
            );
          } else if (state is ResetPasswordSuccess) {
            setState(() {
              _currentStep = ResetPasswordStep.success;
            });
          } else if (state is ResetPasswordFailure) {
            UiUtils.showSnackBar(
              context: context,
              message: state.errorMessage,
              isError: true,
            );
          }
        },
        builder: (context, state) {
          return Stack(
            children: [
              SafeArea(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20.0),
                  child: _buildCurrentStep(),
                ),
              ),
              if (state is RequestPasswordResetInProgress ||
                  state is VerifyResetCodeInProgress ||
                  state is ResetPasswordInProgress)
                const CustomCircularProgressIndicator(),
            ],
          );
        },
      ),
    );
  }
} 