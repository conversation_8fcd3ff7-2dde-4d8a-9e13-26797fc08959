import 'package:eschool_teacher/app/routes.dart';
import 'package:eschool_teacher/data/models/chatUser.dart';
import 'package:eschool_teacher/ui/styles/designSystem.dart';
import 'package:eschool_teacher/ui/widgets/customUserProfileImageWidget.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter/material.dart';
// ignore: depend_on_referenced_packages
import 'package:intl/intl.dart';

class ChatUserItemWidget extends StatelessWidget {
  final ChatUser chatUser;
  final bool showCount;
  const ChatUserItemWidget(
      {super.key, required this.chatUser, this.showCount = true,});

  Widget _buildProfileImage({required String imageUrl}) {
    return Container(
      height: 56,
      width: 56,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: DesignSystem.textTertiary.withValues(alpha: 0.3),
          width: 2,
        ),
        boxShadow: DesignSystem.shadowSm,
      ),
      child: ClipOval(
        child: CustomUserProfileImageWidget(
          profileUrl: imageUrl,
          color: DesignSystem.textSecondary,
        ),
      ),
    );
  }

  String _getDateTimeTextForLastMessage(
      {required BuildContext context, required DateTime dateTime,}) {
    if (dateTime.isToday()) {
      return UiUtils.formatTimeWithDateTime(dateTime, is24: false);
    } else if (dateTime.isYesterday()) {
      return UiUtils.getTranslatedLabel(context, yesterdayKey);
    } else if (dateTime.isCurrentYear()) {
      return DateFormat("dd MMM").format(dateTime);
    } else {
      return DateFormat("dd MMM yyyy").format(dateTime);
    }
  }

  Widget _buildUnreadCounter({required int count}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacingSm,
        vertical: DesignSystem.spacing2xs,
      ),
      decoration: BoxDecoration(
        borderRadius: DesignSystem.borderRadiusFull,
        color: DesignSystem.successColor,
      ),
      child: Text(
        (count > 999) ? "999+" : count.toString(),
        style: DesignSystem.labelSmall.copyWith(
          color: DesignSystem.textOnPrimary,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }

  Widget _buildOnlineIndicator() {
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: DesignSystem.successColor,
        shape: BoxShape.circle,
        border: Border.all(
          color: DesignSystem.surfaceColor,
          width: 2,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final hasUnreadMessages = chatUser.hasUnreadMessages;
    
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: DesignSystem.spacing2xl,
        vertical: DesignSystem.spacingSm,
      ),
      decoration: BoxDecoration(
        color: hasUnreadMessages 
            ? DesignSystem.primaryColor.withValues(alpha: 0.05)
            : DesignSystem.surfaceColor,
        borderRadius: DesignSystem.borderRadiusLg,
        border: Border.all(
          color: hasUnreadMessages 
              ? DesignSystem.primaryColor.withValues(alpha: 0.2)
              : DesignSystem.textTertiary.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: hasUnreadMessages ? DesignSystem.shadowMd : DesignSystem.shadowSm,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.pushNamed(
              context,
              Routes.chatMessages,
              arguments: {"chatUser": chatUser},
            );
          },
          borderRadius: DesignSystem.borderRadiusLg,
          child: Padding(
            padding: DesignSystem.paddingLg,
            child: Row(
              children: [
                _buildProfileImage(imageUrl: chatUser.profileUrl),
                SizedBox(width: DesignSystem.spacingLg),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              chatUser.userName,
                              style: DesignSystem.titleMedium.copyWith(
                                fontWeight: hasUnreadMessages 
                                    ? FontWeight.w600 
                                    : FontWeight.w500,
                                color: hasUnreadMessages 
                                    ? DesignSystem.textPrimary 
                                    : DesignSystem.textSecondary,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (chatUser.lastMessage != null) ...[
                            SizedBox(width: DesignSystem.spacingSm),
                            Text(
                              _getDateTimeTextForLastMessage(
                                context: context,
                                dateTime: chatUser.lastMessage!.sendOrReceiveDateTime,
                              ),
                              style: DesignSystem.bodySmall.copyWith(
                                color: DesignSystem.textTertiary,
                              ),
                            ),
                          ],
                        ],
                      ),
                      SizedBox(height: DesignSystem.spacingXs),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              _getLastMessageText(context),
                              style: DesignSystem.bodySmall.copyWith(
                                color: hasUnreadMessages 
                                    ? DesignSystem.textSecondary 
                                    : DesignSystem.textTertiary,
                                fontWeight: hasUnreadMessages 
                                    ? FontWeight.w500 
                                    : FontWeight.w400,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                          if (hasUnreadMessages && showCount) ...[
                            SizedBox(width: DesignSystem.spacingSm),
                            _buildUnreadCounter(
                              count: chatUser.unreadNotificationsCount,
                            ),
                          ],
                        ],
                      ),
                      if (chatUser.isParent) ...[
                        SizedBox(height: DesignSystem.spacingXs),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: DesignSystem.spacingSm,
                            vertical: DesignSystem.spacing2xs,
                          ),
                          decoration: BoxDecoration(
                            color: DesignSystem.warningColor.withValues(alpha: 0.1),
                            borderRadius: DesignSystem.borderRadiusSm,
                          ),
                          child: Text(
                            "Parent",
                            style: DesignSystem.labelSmall.copyWith(
                              color: DesignSystem.warningColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getLastMessageText(BuildContext context) {
    if (chatUser.hasUnreadMessages && showCount) {
      if ((chatUser.lastMessage?.message.isEmpty ?? true) &&
          (chatUser.lastMessage?.files.isNotEmpty ?? false)) {
        return "${chatUser.lastMessage?.files.length} ${UiUtils.getTranslatedLabel(context, filesReceivedKey)}";
      }
      return chatUser.lastMessage?.message ?? "";
    }
    
    if (chatUser.isParent) {
      return "${UiUtils.getTranslatedLabel(context, childrenKey)}: ${chatUser.childrenNames}";
    }
    
    return "${UiUtils.getTranslatedLabel(context, classKey)}: ${chatUser.className ?? ''}";
  }
}
