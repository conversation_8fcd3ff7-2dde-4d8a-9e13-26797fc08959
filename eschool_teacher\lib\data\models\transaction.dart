class Transaction {
  final String id;
  final double amount;
  final String type; // "lesson_payment", "withdrawal", etc.
  final DateTime date;
  final String status; // "completed", "pending", "cancelled"
  final String? description;

  Transaction({
    required this.id,
    required this.amount,
    required this.type,
    required this.date,
    required this.status,
    this.description,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] as String? ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      type: json['type'] as String? ?? '',
      date: json['date'] != null 
          ? DateTime.parse(json['date'] as String)
          : DateTime.now(),
      status: json['status'] as String? ?? '',
      description: json['description'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'type': type,
      'date': date.toIso8601String(),
      'status': status,
      'description': description,
    };
  }
} 