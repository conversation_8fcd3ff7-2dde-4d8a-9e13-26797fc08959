import 'package:dio/dio.dart';
import 'package:eschool_teacher/data/models/announcement.dart';
import 'package:eschool_teacher/utils/api.dart';
import 'package:file_picker/file_picker.dart';

class AnnouncementRepository {
  Future<Map<String, dynamic>> fetchAnnouncements(
      {int? page, required int subjectId, required int classSectionId,}) async {
    try {
      final Map<String, dynamic> queryParameters = {
        "page": page ?? 0,
        "subject_id": subjectId,
        "class_section_id": classSectionId,
      };
      if (queryParameters['page'] == 0) {
        queryParameters.remove("page");
      }

      final result = await Api.get(
          url: Api.getAnnouncement,
          useAuthToken: true,
          queryParameters: queryParameters,);

      return {
        "announcements": (result['data']['data'] as List)
            .map((e) => Announcement.fromJson(Map.from(e)))
            .toList(),
        "totalPage": result['data']['last_page'] as int,
        "currentPage": result['data']['current_page'] as int,
      };
    } catch (e) {
      throw ApiException(e.toString());
    }
  }

  Future<void> createAnnouncement(
      {required String title,
      required String description,
      required List<PlatformFile> attachments,
      required int classSectionId,
      required int subjectId,}) async {
    try {
      final List<MultipartFile> files = [];
      for (final file in attachments) {
        files.add(await MultipartFile.fromFile(file.path!));
      }
      final Map<String, dynamic> body = {
        "class_section_id": classSectionId,
        "subject_id": subjectId,
        "title": title,
        "description": description,
        "file": files,
      };
      if (files.isEmpty) {
        body.remove('file');
      }
      if (description.isEmpty) {
        body.remove('description');
      }

      await Api.post(
          body: body, url: Api.createAnnouncement, useAuthToken: true,);
    } catch (e) {
      throw ApiException(e.toString());
    }
  }

  Future<void> updateAnnouncement(
      {required String title,
      required String description,
      required List<PlatformFile> attachments,
      required int classSectionId,
      required int subjectId,
      required String announcementId,}) async {
    try {
      final List<MultipartFile> files = [];
      for (final file in attachments) {
        files.add(await MultipartFile.fromFile(file.path!));
      }
      final Map<String, dynamic> body = {
        "announcement_id": announcementId,
        "class_section_id": classSectionId,
        "subject_id": subjectId,
        "title": title,
        "description": description,
        "file": files,
      };
      if (files.isEmpty) {
        body.remove('file');
      }
      if (description.isEmpty) {
        body.remove('description');
      }

      await Api.post(
          body: body, url: Api.updateAnnouncement, useAuthToken: true,);
    } catch (e) {
      throw ApiException(e.toString());
    }
  }

  Future<void> deleteAnnouncement(String announcementId) async {
    try {
      await Api.post(
          body: {"announcement_id": announcementId},
          url: Api.deleteAnnouncement,
          useAuthToken: true,);
    } catch (e) {
      throw ApiException(e.toString());
    }
  }

  Future<Map<String, dynamic>> fetchGeneralAnnouncements({int? page}) async {
    try {
      final Map<String, dynamic> queryParameters = {
        "page": page ?? 0,
        "type": "noticeboard",
      };
      if (queryParameters['page'] == 0) {
        queryParameters.remove("page");
      }

      final result = await Api.get(
          url: Api.generalAnnouncements,
          useAuthToken: true,
          queryParameters: queryParameters,);

      return {
        "announcements": (result['data']['data'] as List)
            .map((e) => Announcement.fromJson(Map.from(e)))
            .toList(),
        "totalPage": result['data']['last_page'] as int,
        "currentPage": result['data']['current_page'] as int,
      };
    } catch (e) {
      throw ApiException(e.toString());
    }
  }
}
