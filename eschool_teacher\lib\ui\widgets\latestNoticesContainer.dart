import 'package:eschool_teacher/cubits/noticeBoardCubit.dart';
import 'package:eschool_teacher/data/models/announcement.dart';
import 'package:eschool_teacher/ui/widgets/announcementDetailsContainer.dart';
import 'package:eschool_teacher/ui/widgets/shimmerLoaders/announcementShimmerLoadingContainer.dart';
import 'package:eschool_teacher/utils/animationConfiguration.dart';
import 'package:eschool_teacher/utils/constants.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LatestNoticesContainer extends StatelessWidget {
  final bool animate;
  const LatestNoticesContainer({
    super.key,
    this.animate = true,
  });

  Widget _announcementsSuccessItems(List<Announcement> announcements) {
    return Column(
      children: List.generate(
        announcements.length,
        (index) => Animate(
          effects: animate
              ? listItemAppearanceEffects(
                  itemIndex: index,
                  totalLoadedItems: announcements.length,
                )
              : null,
          child: AnnouncementDetailsContainer(
            announcement: announcements[index],
          ),
        ),
      ),
    );
  }

  Widget _announcementsLoading() {
    return Column(
      children: List.generate(3, (index) => index)
          .map((notice) => const AnnouncementShimmerLoadingContainer())
          .toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: EdgeInsets.symmetric(
        horizontal: MediaQuery.of(context).size.width *
            UiUtils.screenContentHorizontalPaddingPercentage,
      ),
      child: Column(
        children: [
          Text(
            UiUtils.getTranslatedLabel(context, latestNoticesKey),
            style: TextStyle(
              color: Theme.of(context).colorScheme.secondary,
              fontWeight: FontWeight.w600,
              fontSize: 16.0,
            ),
            textAlign: TextAlign.start,
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * (0.025),
          ),
          BlocBuilder<NoticeBoardCubit, NoticeBoardState>(
            builder: (context, state) {
              if (state is NoticeBoardFetchSuccess) {
                final announcements = state.announcements.length >
                        numberOfLatestNoticesInHomeScreen
                    ? state.announcements
                        .sublist(0, numberOfLatestNoticesInHomeScreen)
                        .toList()
                    : state.announcements;
                return _announcementsSuccessItems(announcements);
              }

              if (state is NoticeBoardFetchInProgress ||
                  state is NoticeBoardInitial) {
                return _announcementsLoading();
              }

              return const SizedBox();
            },
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * (0.025),
          ),
        ],
      ),
    );
  }
} 