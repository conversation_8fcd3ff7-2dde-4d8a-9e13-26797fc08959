import 'package:eschool_teacher/cubits/noticeBoardCubit.dart';
import 'package:eschool_teacher/data/repositories/announcementRepository.dart';
import 'package:eschool_teacher/ui/widgets/announcementDetailsContainer.dart';
import 'package:eschool_teacher/ui/widgets/customAppbar.dart';
import 'package:eschool_teacher/ui/widgets/customRefreshIndicator.dart';
import 'package:eschool_teacher/ui/widgets/errorContainer.dart';
import 'package:eschool_teacher/ui/widgets/noDataContainer.dart';
import 'package:eschool_teacher/ui/widgets/shimmerLoaders/announcementShimmerLoadingContainer.dart';
import 'package:eschool_teacher/utils/animationConfiguration.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class NoticeBoardScreen extends StatefulWidget {
  const NoticeBoardScreen({super.key});

  @override
  State<NoticeBoardScreen> createState() => _NoticeBoardScreenState();

  static Route<dynamic> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => BlocProvider(
        create: (context) => NoticeBoardCubit(AnnouncementRepository()),
        child: const NoticeBoardScreen(),
      ),
    );
  }
}

class _NoticeBoardScreenState extends State<NoticeBoardScreen> {
  late final ScrollController _scrollController = ScrollController()
    ..addListener(_announcementsScrollListener);

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      if (context.mounted) {
        context.read<NoticeBoardCubit>().fetchNoticeBoardDetails();
      }
    });
  }

  void _announcementsScrollListener() {
    if (_scrollController.offset ==
        _scrollController.position.maxScrollExtent) {
      if (context.read<NoticeBoardCubit>().hasMore()) {
        context.read<NoticeBoardCubit>().fetchMoreAnnouncements();
      }
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_announcementsScrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  Widget _buildAnnouncementShimmerLoading() {
    return Column(
      children: List.generate(
        UiUtils.defaultShimmerLoadingContentCount,
        (index) => const AnnouncementShimmerLoadingContainer(),
      ),
    );
  }

  Widget _buildAnnouncementsList() {
    return BlocBuilder<NoticeBoardCubit, NoticeBoardState>(
      builder: (context, state) {
        if (state is NoticeBoardFetchSuccess) {
          if (state.announcements.isEmpty) {
            return const NoDataContainer(titleKey: noAnnouncementsKey);
          }

          return Column(
            children: List.generate(
              state.announcements.length,
              (index) {
                final announcement = state.announcements[index];
                
                // Show loading indicator for pagination
                if (index == state.announcements.length - 1 && 
                    context.read<NoticeBoardCubit>().hasMore()) {
                  if (state.fetchMoreAnnouncementsInProgress) {
                    return Column(
                      children: [
                        Animate(
                          effects: customItemFadeAppearanceEffects(),
                          child: AnnouncementDetailsContainer(
                            announcement: announcement,
                          ),
                        ),
                        const AnnouncementShimmerLoadingContainer(),
                      ],
                    );
                  }
                  
                  if (state.moreAnnouncementsFetchError) {
                    return Column(
                      children: [
                        Animate(
                          effects: customItemFadeAppearanceEffects(),
                          child: AnnouncementDetailsContainer(
                            announcement: announcement,
                          ),
                        ),
                        Center(
                          child: CupertinoButton(
                            child: Text(UiUtils.getTranslatedLabel(context, retryKey)),
                            onPressed: () {
                              context.read<NoticeBoardCubit>().fetchMoreAnnouncements();
                            },
                          ),
                        ),
                      ],
                    );
                  }
                }

                return Animate(
                  effects: customItemFadeAppearanceEffects(),
                  child: AnnouncementDetailsContainer(
                    announcement: announcement,
                  ),
                );
              },
            ),
          );
        }

        if (state is NoticeBoardFetchFailure) {
          return Center(
            child: ErrorContainer(
              errorMessageCode: state.errorMessage,
              onTapRetry: () {
                context.read<NoticeBoardCubit>().fetchNoticeBoardDetails();
              },
            ),
          );
        }

        return _buildAnnouncementShimmerLoading();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          CustomRefreshIndicator(
            displacement: UiUtils.getScrollViewTopPadding(
              context: context,
              appBarHeightPercentage: UiUtils.appBarSmallerHeightPercentage,
            ),
            onRefreshCallback: () {
              context.read<NoticeBoardCubit>().fetchNoticeBoardDetails();
            },
            child: ListView(
              physics: const AlwaysScrollableScrollPhysics(),
              controller: _scrollController,
              padding: EdgeInsets.only(
                left: MediaQuery.of(context).size.width *
                    UiUtils.screenContentHorizontalPaddingPercentage,
                right: MediaQuery.of(context).size.width *
                    UiUtils.screenContentHorizontalPaddingPercentage,
                top: UiUtils.getScrollViewTopPadding(
                  context: context,
                  appBarHeightPercentage: UiUtils.appBarSmallerHeightPercentage,
                ),
              ),
              children: [
                _buildAnnouncementsList(),
              ],
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: CustomAppBar(
              title: UiUtils.getTranslatedLabel(context, latestNoticesKey),
            ),
          ),
        ],
      ),
    );
  }
} 