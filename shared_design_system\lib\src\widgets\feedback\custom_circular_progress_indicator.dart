import 'package:flutter/material.dart';
import '../../tokens/colors.dart';
import '../../tokens/spacing.dart';
import '../../tokens/typography.dart';

/// Shared circular progress indicator following the design system
class CustomCircularProgressIndicator extends StatelessWidget {
  const CustomCircularProgressIndicator({
    super.key,
    this.value,
    this.backgroundColor,
    this.color,
    this.strokeWidth = 4.0,
    this.size,
    this.semanticsLabel,
    this.semanticsValue,
    this.strokeAlign = BorderSide.strokeAlignInside,
    this.strokeCap,
  });

  /// Progress value (0.0 to 1.0) - null for indeterminate
  final double? value;

  /// Background color of the circular track
  final Color? backgroundColor;

  /// Color of the progress indicator
  final Color? color;

  /// Width of the circular stroke
  final double strokeWidth;

  /// Size of the progress indicator
  final double? size;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Semantic value for accessibility
  final String? semanticsValue;

  /// Stroke alignment
  final double strokeAlign;

  /// Stroke cap style
  final StrokeCap? strokeCap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final effectiveColor = color ?? AppColors.primary;
    final effectiveBackgroundColor = backgroundColor ?? AppColors.neutral300;
    final effectiveSize = size ?? AppSpacing.iconXl;

    Widget progressIndicator = CircularProgressIndicator(
      value: value,
      backgroundColor: effectiveBackgroundColor,
      color: effectiveColor,
      strokeWidth: strokeWidth,
      semanticsLabel: semanticsLabel,
      semanticsValue: semanticsValue,
      strokeAlign: strokeAlign,
      strokeCap: strokeCap,
    );

    if (size != null) {
      progressIndicator = SizedBox(
        width: effectiveSize,
        height: effectiveSize,
        child: progressIndicator,
      );
    }

    return progressIndicator;
  }
}

/// Progress indicator with text overlay
class CustomCircularProgressIndicatorWithText extends StatelessWidget {
  const CustomCircularProgressIndicatorWithText({
    super.key,
    required this.value,
    this.text,
    this.backgroundColor,
    this.color,
    this.strokeWidth = 6.0,
    this.size = 80.0,
    this.textStyle,
    this.showPercentage = true,
    this.semanticsLabel,
  });

  /// Progress value (0.0 to 1.0)
  final double value;

  /// Custom text to display (overrides percentage)
  final String? text;

  /// Background color of the circular track
  final Color? backgroundColor;

  /// Color of the progress indicator
  final Color? color;

  /// Width of the circular stroke
  final double strokeWidth;

  /// Size of the progress indicator
  final double size;

  /// Text style
  final TextStyle? textStyle;

  /// Whether to show percentage when no custom text
  final bool showPercentage;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? AppColors.primary;
    final effectiveBackgroundColor = backgroundColor ?? AppColors.neutral300;
    final effectiveTextStyle = textStyle ?? AppTypography.labelLarge.copyWith(
      color: effectiveColor,
      fontWeight: FontWeight.w600,
    );

    final displayText = text ?? 
        (showPercentage ? '${(value * 100).round()}%' : '');

    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Progress indicator
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              value: value,
              backgroundColor: effectiveBackgroundColor,
              color: effectiveColor,
              strokeWidth: strokeWidth,
              semanticsLabel: semanticsLabel,
            ),
          ),
          // Text overlay
          if (displayText.isNotEmpty)
            Text(
              displayText,
              style: effectiveTextStyle,
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }
}

/// Linear progress indicator following the design system
class CustomLinearProgressIndicator extends StatelessWidget {
  const CustomLinearProgressIndicator({
    super.key,
    this.value,
    this.backgroundColor,
    this.color,
    this.minHeight = 4.0,
    this.semanticsLabel,
    this.semanticsValue,
    this.borderRadius,
  });

  /// Progress value (0.0 to 1.0) - null for indeterminate
  final double? value;

  /// Background color of the linear track
  final Color? backgroundColor;

  /// Color of the progress indicator
  final Color? color;

  /// Minimum height of the progress bar
  final double minHeight;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Semantic value for accessibility
  final String? semanticsValue;

  /// Border radius for rounded progress bar
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? AppColors.primary;
    final effectiveBackgroundColor = backgroundColor ?? AppColors.neutral300;
    final effectiveBorderRadius = borderRadius ?? 
        BorderRadius.circular(minHeight / 2);

    return Container(
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        borderRadius: effectiveBorderRadius,
      ),
      child: LinearProgressIndicator(
        value: value,
        backgroundColor: effectiveBackgroundColor,
        color: effectiveColor,
        minHeight: minHeight,
        semanticsLabel: semanticsLabel,
        semanticsValue: semanticsValue,
      ),
    );
  }
}

/// Pre-built progress indicator variants
class AppProgressIndicator {
  AppProgressIndicator._();

  /// Small circular progress indicator
  static Widget small({
    double? value,
    Color? color,
  }) {
    return CustomCircularProgressIndicator(
      value: value,
      color: color,
      size: AppSpacing.iconMd,
      strokeWidth: 2.0,
    );
  }

  /// Medium circular progress indicator (default)
  static Widget medium({
    double? value,
    Color? color,
  }) {
    return CustomCircularProgressIndicator(
      value: value,
      color: color,
      size: AppSpacing.iconXl,
      strokeWidth: 4.0,
    );
  }

  /// Large circular progress indicator
  static Widget large({
    double? value,
    Color? color,
  }) {
    return CustomCircularProgressIndicator(
      value: value,
      color: color,
      size: AppSpacing.iconXxl,
      strokeWidth: 6.0,
    );
  }

  /// Progress indicator with percentage text
  static Widget withPercentage({
    required double value,
    Color? color,
    double size = 80.0,
  }) {
    return CustomCircularProgressIndicatorWithText(
      value: value,
      color: color,
      size: size,
    );
  }

  /// Progress indicator with custom text
  static Widget withText({
    required double value,
    required String text,
    Color? color,
    double size = 80.0,
  }) {
    return CustomCircularProgressIndicatorWithText(
      value: value,
      text: text,
      color: color,
      size: size,
      showPercentage: false,
    );
  }

  /// Linear progress bar
  static Widget linear({
    double? value,
    Color? color,
    double height = 4.0,
  }) {
    return CustomLinearProgressIndicator(
      value: value,
      color: color,
      minHeight: height,
    );
  }

  /// Thick linear progress bar
  static Widget linearThick({
    double? value,
    Color? color,
  }) {
    return CustomLinearProgressIndicator(
      value: value,
      color: color,
      minHeight: 8.0,
    );
  }

  /// Success progress indicator
  static Widget success({
    double? value,
    double? size,
  }) {
    return CustomCircularProgressIndicator(
      value: value,
      color: AppColors.success,
      size: size,
    );
  }

  /// Warning progress indicator
  static Widget warning({
    double? value,
    double? size,
  }) {
    return CustomCircularProgressIndicator(
      value: value,
      color: AppColors.warning,
      size: size,
    );
  }

  /// Error progress indicator
  static Widget error({
    double? value,
    double? size,
  }) {
    return CustomCircularProgressIndicator(
      value: value,
      color: AppColors.error,
      size: size,
    );
  }

  /// Loading overlay with progress indicator
  static Widget overlay({
    required bool isLoading,
    required Widget child,
    String? loadingText,
    Color? backgroundColor,
  }) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: (backgroundColor ?? AppColors.surface).withOpacity(0.8),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CustomCircularProgressIndicator(),
                  if (loadingText != null) ...[
                    const SizedBox(height: AppSpacing.md),
                    Text(
                      loadingText,
                      style: AppTypography.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          ),
      ],
    );
  }
} 