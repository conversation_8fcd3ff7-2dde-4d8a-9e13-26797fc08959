class RecurringPattern {
  final String frequency; // "weekly", "biweekly", etc.
  final int occurrences; // Total number of occurrences
  final List<DateTime>? exceptionDates; // Dates to skip
  
  RecurringPattern({
    required this.frequency,
    required this.occurrences,
    this.exceptionDates,
  });
  
  factory RecurringPattern.fromJson(Map<String, dynamic> json) {
    return RecurringPattern(
      frequency: json['frequency'] as String? ?? 'weekly',
      occurrences: json['occurrences'] as int? ?? 1,
      exceptionDates: json['exception_dates'] != null
          ? List<DateTime>.from(
              (json['exception_dates'] as List).map(
                (dateString) => DateTime.parse(dateString as String),
              ),
            )
          : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'frequency': frequency,
      'occurrences': occurrences,
      'exception_dates': exceptionDates?.map((date) => date.toIso8601String()).toList(),
    };
  }
}

class EnhancedLesson {
  final String id;
  final String title;
  final String type; // "individual", "group"
  final DateTime startTime;
  final DateTime endTime;
  final List<String> studentIds;
  final String? zoomLink;
  final int rescheduleCount; // Track reschedules (max 2)
  final RecurringPattern? recurringPattern; // Null if not recurring
  
  EnhancedLesson({
    required this.id,
    required this.title,
    required this.type,
    required this.startTime,
    required this.endTime,
    required this.studentIds,
    this.zoomLink,
    this.rescheduleCount = 0,
    this.recurringPattern,
  });
  
  factory EnhancedLesson.fromJson(Map<String, dynamic> json) {
    return EnhancedLesson(
      id: json['id'] as String? ?? '',
      title: json['title'] as String? ?? '',
      type: json['type'] as String? ?? 'group',
      startTime: json['start_time'] != null
          ? DateTime.parse(json['start_time'] as String)
          : DateTime.now(),
      endTime: json['end_time'] != null
          ? DateTime.parse(json['end_time'] as String)
          : DateTime.now().add(const Duration(hours: 1)),
      studentIds: json['student_ids'] != null
          ? List<String>.from(json['student_ids'] as List)
          : [],
      zoomLink: json['zoom_link'] as String?,
      rescheduleCount: json['reschedule_count'] as int? ?? 0,
      recurringPattern: json['recurring_pattern'] != null
          ? RecurringPattern.fromJson(json['recurring_pattern'] as Map<String, dynamic>)
          : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime.toIso8601String(),
      'student_ids': studentIds,
      'zoom_link': zoomLink,
      'reschedule_count': rescheduleCount,
      'recurring_pattern': recurringPattern?.toJson(),
    };
  }
  
  // Check if this lesson can be rescheduled (less than 2 reschedules)
  bool get canBeRescheduled => rescheduleCount < 2;
  
  // Create a copy of this lesson with updated properties
  EnhancedLesson copyWith({
    String? id,
    String? title,
    String? type,
    DateTime? startTime,
    DateTime? endTime,
    List<String>? studentIds,
    String? zoomLink,
    int? rescheduleCount,
    RecurringPattern? recurringPattern,
  }) {
    return EnhancedLesson(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      studentIds: studentIds ?? this.studentIds,
      zoomLink: zoomLink ?? this.zoomLink,
      rescheduleCount: rescheduleCount ?? this.rescheduleCount,
      recurringPattern: recurringPattern ?? this.recurringPattern,
    );
  }
} 