import 'package:eschool_teacher/data/models/studyMaterial.dart';
import 'package:eschool_teacher/data/models/subject.dart';

class Assignment {
  Assignment({
    required this.id,
    required this.title,
    required this.description,
    required this.teacherID,
    required this.studentID,
    required this.studentFiles,
    required this.gradingComments,
    required this.maxGrade,
    required this.scoredGrade,
    // Legacy fields for backward compatibility
    this.classSectionId,
    this.subjectId,
    this.name,
    this.instructions,
    this.dueDate,
    this.points,
    this.resubmission,
    this.extraDaysForResubmission,
    this.sessionYearId,
    this.createdAt,
    this.classSection,
    this.studyMaterial,
    this.subject,
  });
  
  // UML diagram fields
  late final String id;
  late final String title;
  late final String description;
  late final String teacherID;
  late final List<String> studentID;
  late final List<String> studentFiles;
  late final List<String> gradingComments;
  late final int maxGrade;
  late final List<int> scoredGrade;
  
  // Legacy fields for backward compatibility
  late final int? classSectionId;
  late final int? subjectId;
  late final String? name;
  late final String? instructions;
  late final DateTime? dueDate;
  late final int? points;
  late final int? resubmission;
  late final int? extraDaysForResubmission;
  late final int? sessionYearId;
  late final String? createdAt;
  late final ClassSection? classSection;
  late final List<StudyMaterial>? studyMaterial;
  late final Subject? subject;

  Assignment.fromJson(Map<String, dynamic> json) {
    // UML diagram fields
    id = json['id']?.toString() ?? "";
    title = json['title'] ?? json['name'] ?? "";
    description = json['description'] ?? json['instructions'] ?? "";
    teacherID = json['teacher_id']?.toString() ?? "";
    studentID = ((json['student_ids'] ?? []) as List).map((e) => e.toString()).toList();
    studentFiles = ((json['student_files'] ?? []) as List).map((e) => e.toString()).toList();
    gradingComments = ((json['grading_comments'] ?? []) as List).map((e) => e.toString()).toList();
    maxGrade = json['max_grade'] ?? json['points'] ?? 0;
    scoredGrade = ((json['scored_grades'] ?? []) as List).map((e) => e as int).toList();
    
    // Legacy fields for backward compatibility
    classSectionId = json['class_section_id'];
    subjectId = json['subject_id'];
    name = json['name'];
    instructions = json["instructions"];
    dueDate = json['due_date'] != null ? DateTime.parse(json['due_date']) : null;
    points = json["points"];
    resubmission = json['resubmission'];
    extraDaysForResubmission = json["extra_days_for_resubmission"];
    sessionYearId = json['session_year_id'];
    createdAt = json['created_at'];
    classSection = json['class_section'] != null ? ClassSection.fromJson(json['class_section']) : null;
    studyMaterial = json['file'] != null 
        ? ((json['file'] as List).map((e) => StudyMaterial.fromJson(Map.from(e))).toList())
        : null;
    subject = json['subject'] != null ? Subject.fromJson(json['subject']) : null;
  }

  // Method to duplicate assignment (as per UML)
  void duplicateAssignment(String newStudentID) {
    // Implementation for duplicating assignment for new student
  }
}

class ClassSection {
  ClassSection({
    required this.id,
    required this.classId,
    required this.sectionId,
    required this.classTeacherId,
    required this.classs,
    required this.section,
  });
  late final int id;
  late final int classId;
  late final int sectionId;
  late final int classTeacherId;
  late final Classs classs;
  late final Section section;

  ClassSection.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    classId = json['class_id'] ?? 0;
    sectionId = json['section_id'] ?? 0;
    classTeacherId = json['class_teacher_id'] ?? 0;
    classs = Classs.fromJson(json['class'] ?? {});
    section = Section.fromJson(json['section'] ?? {});
  }
}

class Classs {
  Classs({
    required this.id,
    required this.name,
    required this.mediumId,
  });
  late final int id;
  late final String name;
  late final int mediumId;

  Classs.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    name = json['name'] ?? "";
    mediumId = json['medium_id'] ?? "";
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['medium_id'] = mediumId;
    return data;
  }
}

class Section {
  Section({
    required this.id,
    required this.name,
  });
  late final int id;
  late final String name;

  Section.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? 0;
    name = json['name'] ?? "";
  }
}
