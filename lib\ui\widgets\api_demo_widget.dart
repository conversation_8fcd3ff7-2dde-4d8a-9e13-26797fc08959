import 'package:flutter/material.dart';
import 'package:eschool/data/services/education_center_service.dart';
import 'package:eschool/data/services/dummy_data_service.dart';

/// Demo Widget showing API-first with dummy data fallback pattern
/// 
/// This widget demonstrates how to use the EducationCenterService
/// and shows the difference between API calls and dummy data fallback.
class ApiDemoWidget extends StatefulWidget {
  const ApiDemoWidget({Key? key}) : super(key: key);

  @override
  State<ApiDemoWidget> createState() => _ApiDemoWidgetState();
}

class _ApiDemoWidgetState extends State<ApiDemoWidget> {
  final EducationCenterService _educationService = EducationCenterService();
  
  bool _isLoading = false;
  String _lastOperation = '';
  Map<String, dynamic>? _lastResult;
  String? _error;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API Demo'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            _buildStatusCard(),
            const SizedBox(height: 16),
            
            // Control Buttons
            _buildControlButtons(),
            const SizedBox(height: 16),
            
            // API Test Buttons
            _buildApiTestButtons(),
            const SizedBox(height: 16),
            
            // Results Section
            Expanded(child: _buildResultsSection()),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    final status = _educationService.getServiceStatus();
    final dummyDataConfig = status['dummyDataConfig'] as Map<String, dynamic>;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Service Status',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text('Dummy Data Mode: ${dummyDataConfig['useDummyData']}'),
            Text('API Failed: ${dummyDataConfig['apiConnectionFailed']}'),
            Text('Debug Mode: ${dummyDataConfig['debugMode']}'),
            Text('Override: ${dummyDataConfig['override']}'),
          ],
        ),
      ),
    );
  }

  Widget _buildControlButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Controls',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _educationService.enableDummyDataMode();
                      });
                    },
                    child: const Text('Enable Dummy Data'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _educationService.disableDummyDataMode();
                      });
                    },
                    child: const Text('Disable Dummy Data'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _educationService.simulateApiFailure();
                      });
                    },
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                    child: const Text('Simulate API Failure'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _educationService.resetApiStatus();
                      });
                    },
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                    child: const Text('Reset API Status'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildApiTestButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'API Tests',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _testApiCall('subjects'),
                    child: const Text('Get Subjects'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _testApiCall('packages'),
                    child: const Text('Get Packages'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _testApiCall('lessons'),
                    child: const Text('Get Lessons'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : () => _testApiCall('assets'),
                    child: const Text('Get Assets'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : () => _runDemo(),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
                child: const Text('Run Full Demo'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Results ${_lastOperation.isNotEmpty ? '($_lastOperation)' : ''}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _error != null
                      ? Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            border: Border.all(color: Colors.red),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'Error: $_error',
                            style: const TextStyle(color: Colors.red),
                          ),
                        )
                      : _lastResult != null
                          ? SingleChildScrollView(
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.green.shade50,
                                  border: Border.all(color: Colors.green),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  _formatResult(_lastResult!),
                                  style: const TextStyle(fontFamily: 'monospace'),
                                ),
                              ),
                            )
                          : const Center(
                              child: Text('No results yet. Try an API call!'),
                            ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testApiCall(String type) async {
    setState(() {
      _isLoading = true;
      _lastOperation = type;
      _error = null;
      _lastResult = null;
    });

    try {
      Map<String, dynamic> result;
      
      switch (type) {
        case 'subjects':
          result = await _educationService.getSubjects();
          break;
        case 'packages':
          final packages = await _educationService.getPackages();
          result = {'success': true, 'data': packages, 'count': packages.length};
          break;
        case 'lessons':
          result = await _educationService.getLessons(subjectId: 1);
          break;
        case 'assets':
          result = await _educationService.getDigitalAssets();
          break;
        default:
          throw Exception('Unknown API type: $type');
      }

      setState(() {
        _lastResult = result;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _runDemo() async {
    setState(() {
      _isLoading = true;
      _lastOperation = 'Full Demo';
      _error = null;
      _lastResult = null;
    });

    try {
      await _educationService.demonstrateApiPattern();
      setState(() {
        _lastResult = {
          'success': true,
          'message': 'Demo completed successfully! Check debug console for details.',
          'timestamp': DateTime.now().toIso8601String(),
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  String _formatResult(Map<String, dynamic> result) {
    final buffer = StringBuffer();
    
    buffer.writeln('Success: ${result['success'] ?? 'unknown'}');
    buffer.writeln('Message: ${result['message'] ?? 'No message'}');
    
    if (result['data'] != null) {
      final data = result['data'];
      if (data is List) {
        buffer.writeln('Data Count: ${data.length}');
        if (data.isNotEmpty) {
          buffer.writeln('First Item: ${data.first.toString().substring(0, 100)}...');
        }
      } else if (data is Map) {
        buffer.writeln('Data Keys: ${data.keys.join(', ')}');
      } else {
        buffer.writeln('Data: ${data.toString()}');
      }
    }
    
    if (result['count'] != null) {
      buffer.writeln('Count: ${result['count']}');
    }
    
    return buffer.toString();
  }
}
