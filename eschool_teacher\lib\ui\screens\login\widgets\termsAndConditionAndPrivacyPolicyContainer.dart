import 'package:eschool_teacher/app/routes.dart';
import 'package:eschool_teacher/utils/labelKeys.dart';
import 'package:eschool_teacher/utils/uiUtils.dart';
import 'package:flutter/material.dart';

class TermsAndConditionAndPrivacyPolicyContainer extends StatelessWidget {
  const TermsAndConditionAndPrivacyPolicyContainer({super.key});
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              UiUtils.getTranslatedLabel(
                  context, termsAndConditionAgreementKey,),
              style: TextStyle(
                  fontSize: 13,
                  color: Theme.of(context)
                      .colorScheme
                      .secondary
                      .withValues(alpha: 0.8),
                  fontWeight: FontWeight.bold,),
            ),
          ],
        ),
        const SizedBox(
          height: 4,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            InkWell(
              onTap: () {
                Navigator.of(context).pushNamed(Routes.termsAndCondition);
              },
              child: Text(
                UiUtils.getTranslatedLabel(context, termsAndConditionKey),
                style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    decoration: TextDecoration.underline,
                    fontWeight: FontWeight.bold,
                    fontSize: 13,),
              ),
            ),
            const SizedBox(
              width: 5.0,
            ),
            Text("&",
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    color: Theme.of(context).colorScheme.secondary,
                    fontWeight: FontWeight.bold,),),
            const SizedBox(
              width: 5.0,
            ),
            InkWell(
              onTap: () {
                Navigator.of(context).pushNamed(Routes.privacyPolicy);
              },
              child: Text(
                UiUtils.getTranslatedLabel(context, privacyPolicyKey),
                style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    decoration: TextDecoration.underline,
                    fontWeight: FontWeight.bold,
                    fontSize: 13,),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
