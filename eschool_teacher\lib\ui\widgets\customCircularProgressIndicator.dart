import 'package:eschool_teacher/ui/styles/colors.dart';
import 'package:flutter/material.dart';

class CustomCircularProgressIndicator extends StatelessWidget {
  final Color backgroundColor;
  final Color color;
  final double? strokeWidth;
  final Color? indicatorColor;
  final double? widthAndHeight;

  const CustomCircularProgressIndicator({
    Key? key,
    this.backgroundColor = Colors.black26,
    this.color = primaryColor,
    this.strokeWidth,
    this.indicatorColor,
    this.widthAndHeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (widthAndHeight != null) {
      return SizedBox(
        width: widthAndHeight,
        height: widthAndHeight,
        child: CircularProgressIndicator(
          color: indicatorColor ?? color,
          strokeWidth: strokeWidth ?? 3.0,
        ),
      );
    }
    
    return Container(
      color: backgroundColor,
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: Center(
        child: CircularProgressIndicator(
          color: indicatorColor ?? color,
          strokeWidth: strokeWidth ?? 4.0,
        ),
      ),
    );
  }
}
